"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AnalyticsPageContent() {\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelAnalytics, setModelAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configAnalytics, setConfigAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Budget settings\n    const [monthlyBudget, setMonthlyBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Default $100/month\n    const [showBudgetSettings, setShowBudgetSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Build query parameters for current period\n                const params = new URLSearchParams();\n                let currentStartDate;\n                let currentEndDate = new Date();\n                if (startDate && endDate) {\n                    currentStartDate = new Date(startDate);\n                    currentEndDate = new Date(endDate);\n                    params.append('startDate', currentStartDate.toISOString());\n                    params.append('endDate', currentEndDate.toISOString());\n                } else if (timeRange) {\n                    currentStartDate = new Date();\n                    currentStartDate.setDate(currentStartDate.getDate() - parseInt(timeRange));\n                    params.append('startDate', currentStartDate.toISOString());\n                }\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                // Build query parameters for previous period (for comparison)\n                const prevParams = new URLSearchParams();\n                const periodLength = currentEndDate.getTime() - currentStartDate.getTime();\n                const prevStartDate = new Date(currentStartDate.getTime() - periodLength);\n                const prevEndDate = new Date(currentStartDate.getTime());\n                prevParams.append('startDate', prevStartDate.toISOString());\n                prevParams.append('endDate', prevEndDate.toISOString());\n                if (selectedConfig) {\n                    prevParams.append('customApiConfigId', selectedConfig);\n                }\n                // Fetch multiple analytics views\n                const [providerResponse, modelResponse, timeSeriesResponse, previousPeriodResponse] = await Promise.all([\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=model\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\")),\n                    fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=day\"))\n                ]);\n                if (!providerResponse.ok || !modelResponse.ok || !timeSeriesResponse.ok) {\n                    throw new Error('Failed to fetch analytics data');\n                }\n                const providerData = await providerResponse.json();\n                const modelData = await modelResponse.json();\n                const timeSeriesResult = await timeSeriesResponse.json();\n                const previousPeriodResult = previousPeriodResponse.ok ? await previousPeriodResponse.json() : null;\n                setAnalyticsData(providerData);\n                setModelAnalytics(modelData);\n                setPreviousPeriodData(previousPeriodResult);\n                // Process time series data\n                const processedTimeSeries = timeSeriesResult.grouped_data.map({\n                    \"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\": (item)=>({\n                            period: item.period,\n                            cost: item.cost,\n                            requests: item.requests,\n                            tokens: item.input_tokens + item.output_tokens\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\"]);\n                setTimeSeriesData(processedTimeSeries);\n            } catch (err) {\n                setError(err.message);\n                console.error('Error fetching analytics:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        fetchAnalyticsData,\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    const calculateTrend = (current, previous)=>{\n        if (previous === 0) return {\n            percentage: 0,\n            isPositive: true\n        };\n        const percentage = (current - previous) / previous * 100;\n        return {\n            percentage: Math.abs(percentage),\n            isPositive: percentage >= 0\n        };\n    };\n    const generateCostAlerts = ()=>{\n        const alerts = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        if (!summary) return alerts;\n        // Budget alert\n        const projectedMonthlyCost = summary.total_cost / parseInt(timeRange) * 30;\n        if (projectedMonthlyCost > monthlyBudget * 0.8) {\n            alerts.push({\n                type: projectedMonthlyCost > monthlyBudget ? 'danger' : 'warning',\n                title: projectedMonthlyCost > monthlyBudget ? 'Budget Exceeded' : 'Budget Warning',\n                message: \"Projected monthly cost: \".concat(formatCurrency(projectedMonthlyCost), \" (Budget: \").concat(formatCurrency(monthlyBudget), \")\"),\n                value: \"\".concat((projectedMonthlyCost / monthlyBudget * 100).toFixed(0), \"%\")\n            });\n        }\n        // Success rate alert\n        if (summary.success_rate < 95) {\n            alerts.push({\n                type: summary.success_rate < 90 ? 'danger' : 'warning',\n                title: 'Low Success Rate',\n                message: \"Current success rate is \".concat(summary.success_rate.toFixed(1), \"%. Consider reviewing failed requests.\"),\n                value: \"\".concat(summary.success_rate.toFixed(1), \"%\")\n            });\n        }\n        // High cost per request alert\n        if (summary.average_cost_per_request > 0.01) {\n            alerts.push({\n                type: 'warning',\n                title: 'High Cost Per Request',\n                message: \"Average cost per request is \".concat(formatCurrency(summary.average_cost_per_request), \". Consider optimizing model usage.\"),\n                value: formatCurrency(summary.average_cost_per_request)\n            });\n        }\n        return alerts;\n    };\n    const generateRecommendations = ()=>{\n        const recommendations = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        const providers = (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data) || [];\n        const models = (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data) || [];\n        if (!summary) return recommendations;\n        // Cost optimization recommendations\n        const mostExpensiveModel = models.sort((a, b)=>b.cost - a.cost)[0];\n        if (mostExpensiveModel && mostExpensiveModel.cost > summary.total_cost * 0.3) {\n            const potentialSavings = mostExpensiveModel.cost * 0.2; // Assume 20% savings possible\n            recommendations.push({\n                type: 'cost_optimization',\n                title: 'Optimize Expensive Model Usage',\n                description: \"\".concat(mostExpensiveModel.name, \" accounts for \").concat((mostExpensiveModel.cost / summary.total_cost * 100).toFixed(1), \"% of your costs. Consider using a more cost-effective alternative for simpler tasks.\"),\n                potential_savings: potentialSavings,\n                icon: _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n            });\n        }\n        // Performance recommendations\n        if (summary.success_rate < 98) {\n            recommendations.push({\n                type: 'performance',\n                title: 'Improve Request Reliability',\n                description: \"Your success rate is \".concat(summary.success_rate.toFixed(1), \"%. Implement retry logic and error handling to improve reliability.\"),\n                icon: _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            });\n        }\n        // Efficiency recommendations\n        const avgTokensPerRequest = (summary.total_input_tokens + summary.total_output_tokens) / summary.total_requests;\n        if (avgTokensPerRequest > 1000) {\n            recommendations.push({\n                type: 'efficiency',\n                title: 'Optimize Token Usage',\n                description: \"Average \".concat(formatNumber(avgTokensPerRequest), \" tokens per request. Consider breaking down large prompts or using more efficient models.\"),\n                icon: _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n            });\n        }\n        return recommendations;\n    };\n    const resetFilters = ()=>{\n        setTimeRange('30');\n        setSelectedConfig('');\n        setStartDate('');\n        setEndDate('');\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-6\",\n                    children: \"\\uD83D\\uDCCA Advanced Analytics\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: [\n                                \"Error loading analytics: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"btn-primary\",\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const costEfficiency = summary ? summary.total_cost / Math.max(summary.successful_requests, 1) : 0;\n    const projectedMonthlyCost = summary ? summary.total_cost / parseInt(timeRange) * 30 : 0;\n    // Generate insights\n    const costAlerts = generateCostAlerts();\n    const recommendations = generateRecommendations();\n    // Calculate trends vs previous period\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : null;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : null;\n    // Professional Line Chart Component (like reference)\n    const ProfessionalLineChart = (param)=>{\n        let { data } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 360,\n                columnNumber: 9\n            }, this);\n        }\n        const maxCost = Math.max(...data.map((d)=>d.cost));\n        const minCost = Math.min(...data.map((d)=>d.cost));\n        const maxRequests = Math.max(...data.map((d)=>d.requests));\n        const minRequests = Math.min(...data.map((d)=>d.requests));\n        const costRange = maxCost - minCost || 1;\n        const requestRange = maxRequests - minRequests || 1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-64 w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-full h-full\",\n                viewBox: \"0 0 600 200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"costGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"requestGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                                id: \"grid\",\n                                width: \"40\",\n                                height: \"40\",\n                                patternUnits: \"userSpaceOnUse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 40 0 L 0 0 0 40\",\n                                    fill: \"none\",\n                                    stroke: \"#f1f5f9\",\n                                    strokeWidth: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"600\",\n                        height: \"200\",\n                        fill: \"url(#grid)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#costGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#requestGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#8b5cf6\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#06b6d4\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    data.map((d, i)=>{\n                        const x = i / Math.max(data.length - 1, 1) * 600;\n                        const costY = 200 - (d.cost - minCost) / costRange * 160;\n                        const requestY = 200 - (d.requests - minRequests) / requestRange * 160;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: costY,\n                                    r: \"3\",\n                                    fill: \"#8b5cf6\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatCurrency(d.cost))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: requestY,\n                                    r: \"3\",\n                                    fill: \"#06b6d4\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatNumber(d.requests), \" requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 15\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, this);\n    };\n    // Professional Donut Chart Component (like reference)\n    const ProfessionalDonutChart = (param)=>{\n        let { data, total } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-48 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this);\n        }\n        // Professional color palette like the reference\n        const colors = [\n            '#f43f5e',\n            '#8b5cf6',\n            '#06b6d4',\n            '#10b981',\n            '#f59e0b',\n            '#ef4444'\n        ];\n        const radius = 70;\n        const strokeWidth = 16;\n        const normalizedRadius = radius - strokeWidth * 0.5;\n        const circumference = normalizedRadius * 2 * Math.PI;\n        let cumulativePercentage = 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"160\",\n                            height: \"160\",\n                            className: \"transform -rotate-90\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"80\",\n                                    cy: \"80\",\n                                    r: normalizedRadius,\n                                    stroke: \"#f8fafc\",\n                                    strokeWidth: strokeWidth,\n                                    fill: \"transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                data.map((item, index)=>{\n                                    const percentage = item.cost / total * 100;\n                                    const strokeDasharray = \"\".concat(percentage / 100 * circumference, \" \").concat(circumference);\n                                    const strokeDashoffset = -(cumulativePercentage / 100 * circumference);\n                                    cumulativePercentage += percentage;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"80\",\n                                        cy: \"80\",\n                                        r: normalizedRadius,\n                                        stroke: colors[index % colors.length],\n                                        strokeWidth: strokeWidth,\n                                        strokeDasharray: strokeDasharray,\n                                        strokeDashoffset: strokeDashoffset,\n                                        fill: \"transparent\",\n                                        strokeLinecap: \"round\",\n                                        className: \"transition-all duration-300 hover:opacity-80 cursor-pointer\",\n                                        style: {\n                                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                        }\n                                    }, index, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: data.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Providers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 w-full\",\n                    children: data.slice(0, 4).map((item, index)=>{\n                        const percentage = (item.cost / total * 100).toFixed(1);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors duration-150\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mr-3 shadow-sm\",\n                                            style: {\n                                                backgroundColor: colors[index]\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700 capitalize\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-semibold text-gray-900\",\n                                            children: [\n                                                percentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: formatCurrency(item.cost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 508,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#1B1C1D] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\",\n                                        children: \"Analytics Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Track your LLM usage, costs, and performance across all providers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: timeRange,\n                                    onChange: (e)=>setTimeRange(e.target.value),\n                                    className: \"px-4 py-2 bg-[#2A2B2C] border border-white/20 rounded-lg text-sm text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"7\",\n                                            children: \"Last 7 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"30\",\n                                            children: \"Last 30 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"90\",\n                                            children: \"Last 90 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 inline\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, this),\n                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Advanced Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Time Range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: timeRange,\n                                            onChange: (e)=>setTimeRange(e.target.value),\n                                            className: \"input-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"7\",\n                                                    children: \"Last 7 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"30\",\n                                                    children: \"Last 30 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"90\",\n                                                    children: \"Last 90 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"365\",\n                                                    children: \"Last year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"API Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedConfig,\n                                            onChange: (e)=>setSelectedConfig(e.target.value),\n                                            className: \"input-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"All Configurations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: config.id,\n                                                        children: config.name\n                                                    }, config.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: startDate,\n                                            onChange: (e)=>setStartDate(e.target.value),\n                                            className: \"input-field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: endDate,\n                                            onChange: (e)=>setEndDate(e.target.value),\n                                            className: \"input-field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"btn-primary\",\n                                    children: \"Apply Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetFilters,\n                                    className: \"btn-secondary\",\n                                    children: \"Reset Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Usage Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Cost trends over time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-purple-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-cyan-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfessionalLineChart, {\n                                    data: timeSeriesData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 pt-6 border-t border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                                                children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total spend this period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-1\",\n                                            children: \"Cost Distribution\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"By provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 13\n                                }, this),\n                                (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.length) && summary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfessionalDonutChart, {\n                                    data: analyticsData.grouped_data,\n                                    total: summary.total_cost\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"No provider data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 9\n                }, this),\n                summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-orange-500/20 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-5 w-5 text-[#ff6b35]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm \".concat(costTrend.isPositive ? 'text-red-500' : 'text-green-500'),\n                                                children: [\n                                                    costTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    costTrend.percentage.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: formatCurrency(summary.total_cost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Total spend\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                formatCurrency(summary.average_cost_per_request),\n                                                \" avg per request\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 770,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 747,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-lg \".concat(summary.success_rate >= 95 ? 'bg-green-500/20' : 'bg-red-500/20'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5 \".concat(summary.success_rate >= 95 ? 'text-green-400' : 'text-red-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm \".concat(summary.success_rate >= 95 ? 'text-green-400' : 'text-red-400'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    summary.success_rate >= 95 ? 'Excellent' : 'Needs attention'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: [\n                                                summary.success_rate.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Success rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                formatNumber(summary.successful_requests),\n                                                \" of \",\n                                                formatNumber(summary.total_requests),\n                                                \" requests\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-purple-500/20 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-purple-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(summary.total_input_tokens + summary.total_output_tokens)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                formatNumber(summary.total_input_tokens),\n                                                \" input • \",\n                                                formatNumber(summary.total_output_tokens),\n                                                \" output\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-indigo-50 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm \".concat(projectedMonthlyCost > monthlyBudget ? 'text-red-500' : 'text-green-500'),\n                                                children: [\n                                                    projectedMonthlyCost > monthlyBudget ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    projectedMonthlyCost > monthlyBudget ? 'Over budget' : 'On track'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatCurrency(projectedMonthlyCost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Projected monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"Budget: \",\n                                                formatCurrency(monthlyBudget)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 745,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                            children: \"Provider Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Detailed breakdown by provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 13\n                                }, this),\n                                (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Provider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Share\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 870,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-gray-50\",\n                                                children: analyticsData.grouped_data.sort((a, b)=>b.cost - a.cost).map((provider, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444',\n                                                        '#8b5cf6'\n                                                    ];\n                                                    const percentage = (provider.cost / ((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 1) * 100).toFixed(1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 883,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900 capitalize\",\n                                                                            children: provider.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 887,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-gray-900\",\n                                                                children: formatCurrency(provider.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-600\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No provider data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 908,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 857,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                            children: \"Top Models\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Most expensive models by cost\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 13\n                                }, this),\n                                (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Model\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 929,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Avg/Request\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-gray-50\",\n                                                children: modelAnalytics.grouped_data.sort((a, b)=>b.cost - a.cost).slice(0, 5).map((model, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444'\n                                                    ];\n                                                    const avgCost = model.cost / Math.max(model.requests, 1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: model.name.length > 20 ? model.name.substring(0, 20) + '...' : model.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 949,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                                    children: [\n                                                                                        formatNumber(model.input_tokens + model.output_tokens),\n                                                                                        \" tokens\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 950,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 948,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-gray-900\",\n                                                                children: formatCurrency(model.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-600\",\n                                                                children: formatNumber(model.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-600\",\n                                                                children: formatCurrency(avgCost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, model.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No model data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 916,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 855,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 11\n                                }, this),\n                                \"Performance Insights\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-lg bg-green-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600 mb-2\",\n                                            children: summary ? formatNumber(summary.total_input_tokens + summary.total_output_tokens) : 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total Tokens Processed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 991,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: summary ? \"\".concat(formatNumber(summary.total_input_tokens), \" in • \").concat(formatNumber(summary.total_output_tokens), \" out\") : ''\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-lg bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600 mb-2\",\n                                            children: summary ? formatNumber(summary.total_requests) : 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 998,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total API Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: summary ? \"\".concat((summary.total_requests / parseInt(timeRange)).toFixed(1), \" per day avg\") : ''\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-lg bg-purple-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600 mb-2\",\n                                            children: summary ? formatCurrency(summary.average_cost_per_request) : '$0.00'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Average Cost per Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Across all providers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 981,\n                    columnNumber: 7\n                }, this),\n                (costAlerts.length > 0 || recommendations.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        costAlerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-[#ff6b35]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1027,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Alerts & Warnings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Important notifications about your usage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1025,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: costAlerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl border-l-4 \".concat(alert.type === 'danger' ? 'border-red-500 bg-red-50' : alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50' : 'border-blue-500 bg-blue-50'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-3 mt-0.5 \".concat(alert.type === 'danger' ? 'text-red-600' : alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: alert.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: alert.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            alert.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold mt-2 \".concat(alert.type === 'danger' ? 'text-red-600' : alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600'),\n                                                                children: alert.value\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1032,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 15\n                        }, this),\n                        recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1073,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Smart Recommendations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1072,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"AI-powered optimization suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1076,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-lg bg-white shadow-sm mr-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(rec.icon, {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: rec.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: rec.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            rec.potential_savings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs bg-green-100 text-green-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 1090,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \"Save \",\n                                                                    formatCurrency(rec.potential_savings)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1089,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1070,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1021,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Budget Management\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Monitor and control your spending\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowBudgetSettings(!showBudgetSettings),\n                                    className: \"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200\",\n                                    children: showBudgetSettings ? 'Hide Settings' : 'Configure'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1106,\n                            columnNumber: 11\n                        }, this),\n                        showBudgetSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-gray-50 rounded-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Monthly Budget (USD)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: monthlyBudget,\n                                            onChange: (e)=>setMonthlyBudget(parseFloat(e.target.value) || 0),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                            placeholder: \"100.00\",\n                                            step: \"0.01\",\n                                            min: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1128,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"• Alerts trigger at 80% (warning) and 100% (danger) of budget\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"• Current projection: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: formatCurrency(projectedMonthlyCost)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1140,\n                                                    columnNumber: 42\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1138,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1123,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: formatCurrency(monthlyBudget)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Monthly Budget\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-orange-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-[#ff6b35]\",\n                                            children: formatCurrency(projectedMonthlyCost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Projected Spend\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-green-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: formatCurrency(Math.max(0, monthlyBudget - projectedMonthlyCost))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: projectedMonthlyCost > monthlyBudget ? 'Over Budget' : 'Remaining'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1105,\n                    columnNumber: 9\n                }, this),\n                previousSummary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1176,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Period Comparison\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1175,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Performance vs previous period\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1179,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1174,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                            children: \"Cost Change\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat((costTrend === null || costTrend === void 0 ? void 0 : costTrend.isPositive) ? 'text-red-600' : 'text-green-600'),\n                                            children: [\n                                                (costTrend === null || costTrend === void 0 ? void 0 : costTrend.isPositive) ? '+' : '-',\n                                                costTrend === null || costTrend === void 0 ? void 0 : costTrend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                formatCurrency(previousSummary.total_cost),\n                                                \" → \",\n                                                formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                            children: \"Request Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat((requestTrend === null || requestTrend === void 0 ? void 0 : requestTrend.isPositive) ? 'text-green-600' : 'text-red-600'),\n                                            children: [\n                                                (requestTrend === null || requestTrend === void 0 ? void 0 : requestTrend.isPositive) ? '+' : '-',\n                                                requestTrend === null || requestTrend === void 0 ? void 0 : requestTrend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                formatNumber(previousSummary.total_requests),\n                                                \" → \",\n                                                formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                            children: \"Efficiency\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(((summary === null || summary === void 0 ? void 0 : summary.average_cost_per_request) || 0) < previousSummary.average_cost_per_request ? 'text-green-600' : 'text-red-600'),\n                                            children: ((summary === null || summary === void 0 ? void 0 : summary.average_cost_per_request) || 0) < previousSummary.average_cost_per_request ? '↑' : '↓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                formatCurrency(previousSummary.average_cost_per_request),\n                                                \" → \",\n                                                formatCurrency((summary === null || summary === void 0 ? void 0 : summary.average_cost_per_request) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1202,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1181,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1173,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 586,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 585,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"qgClNvt3fYHqAHTRr8MfhVCyyr4=\");\n_c = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1226,\n                            columnNumber: 11\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 11\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1225,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1232,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1233,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1234,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1231,\n                            columnNumber: 13\n                        }, void 0))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1229,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1224,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1240,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 1223,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AnalyticsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AnalyticsPageContent\");\n$RefreshReg$(_c1, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});