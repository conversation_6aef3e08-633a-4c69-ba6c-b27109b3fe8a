/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowPathIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n    }));\n}\n_c = ArrowPathIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowPathIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowPathIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChartBarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n    }));\n}\n_c = ChartBarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChartBarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartBarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChartPieIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z\"\n    }));\n}\n_c = ChartPieIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChartPieIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartPieIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CheckCircleIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = CheckCircleIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CheckCircleIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CheckCircleIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ClockIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = ClockIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ClockIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ClockIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0Nsb2NrSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQjtBQUMvQixTQUFTQyxVQUFVLEtBSWxCLEVBQUVDLE1BQU07UUFKVSxFQUNqQkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1AsR0FBR0MsT0FDSixHQUprQjtJQUtqQixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0w7QUFDRjtLQXRCU2pCO0FBdUJULE1BQU1rQixhQUFhLFdBQVcsR0FBR25CLDZDQUFnQixDQUFDQzs7QUFDbEQsaUVBQWVrQixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcQGhlcm9pY29uc1xccmVhY3RcXDI0XFxvdXRsaW5lXFxlc21cXENsb2NrSWNvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIENsb2NrSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTEyIDZ2Nmg0LjVtNC41IDBhOSA5IDAgMSAxLTE4IDAgOSA5IDAgMCAxIDE4IDBaXCJcbiAgfSkpO1xufVxuY29uc3QgRm9yd2FyZFJlZiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuZm9yd2FyZFJlZihDbG9ja0ljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOlsiUmVhY3QiLCJDbG9ja0ljb24iLCJzdmdSZWYiLCJ0aXRsZSIsInRpdGxlSWQiLCJwcm9wcyIsImNyZWF0ZUVsZW1lbnQiLCJPYmplY3QiLCJhc3NpZ24iLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlV2lkdGgiLCJzdHJva2UiLCJyZWYiLCJpZCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImQiLCJGb3J3YXJkUmVmIiwiZm9yd2FyZFJlZiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CpuChipIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z\"\n    }));\n}\n_c = CpuChipIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CpuChipIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CpuChipIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CurrencyDollarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = CurrencyDollarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CurrencyDollarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CurrencyDollarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HandThumbUpIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/HandThumbUpIcon.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction HandThumbUpIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6.633 10.25c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 0 1 2.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 0 0 .322-1.672V2.75a.75.75 0 0 1 .75-.75 2.25 2.25 0 0 1 2.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282m0 0h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 0 1-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 0 0-1.423-.23H5.904m10.598-9.75H14.25M5.904 18.5c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 0 1-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 9.953 4.167 9.5 5 9.5h1.053c.472 0 .745.556.5.96a8.958 8.958 0 0 0-1.302 4.665c0 1.194.232 2.333.654 3.375Z\"\n    }));\n}\n_c = HandThumbUpIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(HandThumbUpIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"HandThumbUpIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HandThumbUpIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/analytics/page.tsx */ \"(app-pages-browser)/./src/app/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1JvS2V5JTIwQXBwJTVDJTVDcm9rZXktYXBwJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYW5hbHl0aWNzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBNEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhbmFseXRpY3NcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HandThumbUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AnalyticsPageContent() {\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelAnalytics, setModelAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configAnalytics, setConfigAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Budget settings\n    const [monthlyBudget, setMonthlyBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Default $100/month\n    const [showBudgetSettings, setShowBudgetSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Build query parameters for current period\n                const params = new URLSearchParams();\n                let currentStartDate;\n                let currentEndDate = new Date();\n                if (startDate && endDate) {\n                    currentStartDate = new Date(startDate);\n                    currentEndDate = new Date(endDate);\n                    params.append('startDate', currentStartDate.toISOString());\n                    params.append('endDate', currentEndDate.toISOString());\n                } else if (timeRange) {\n                    currentStartDate = new Date();\n                    currentStartDate.setDate(currentStartDate.getDate() - parseInt(timeRange));\n                    params.append('startDate', currentStartDate.toISOString());\n                }\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                // Build query parameters for previous period (for comparison)\n                const prevParams = new URLSearchParams();\n                const periodLength = currentEndDate.getTime() - currentStartDate.getTime();\n                const prevStartDate = new Date(currentStartDate.getTime() - periodLength);\n                const prevEndDate = new Date(currentStartDate.getTime());\n                prevParams.append('startDate', prevStartDate.toISOString());\n                prevParams.append('endDate', prevEndDate.toISOString());\n                if (selectedConfig) {\n                    prevParams.append('customApiConfigId', selectedConfig);\n                }\n                // Fetch multiple analytics views\n                const [providerResponse, modelResponse, timeSeriesResponse, previousPeriodResponse] = await Promise.all([\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=model\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\")),\n                    fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=day\"))\n                ]);\n                if (!providerResponse.ok || !modelResponse.ok || !timeSeriesResponse.ok) {\n                    throw new Error('Failed to fetch analytics data');\n                }\n                const providerData = await providerResponse.json();\n                const modelData = await modelResponse.json();\n                const timeSeriesResult = await timeSeriesResponse.json();\n                const previousPeriodResult = previousPeriodResponse.ok ? await previousPeriodResponse.json() : null;\n                setAnalyticsData(providerData);\n                setModelAnalytics(modelData);\n                setPreviousPeriodData(previousPeriodResult);\n                // Process time series data\n                const processedTimeSeries = timeSeriesResult.grouped_data.map({\n                    \"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\": (item)=>({\n                            period: item.period,\n                            cost: item.cost,\n                            requests: item.requests,\n                            tokens: item.input_tokens + item.output_tokens\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\"]);\n                setTimeSeriesData(processedTimeSeries);\n            } catch (err) {\n                setError(err.message);\n                console.error('Error fetching analytics:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        fetchAnalyticsData,\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    const calculateTrend = (current, previous)=>{\n        if (previous === 0) return {\n            percentage: 0,\n            isPositive: true\n        };\n        const percentage = (current - previous) / previous * 100;\n        return {\n            percentage: Math.abs(percentage),\n            isPositive: percentage >= 0\n        };\n    };\n    const generateCostAlerts = ()=>{\n        const alerts = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        if (!summary) return alerts;\n        // Budget alert\n        const projectedMonthlyCost = summary.total_cost / parseInt(timeRange) * 30;\n        if (projectedMonthlyCost > monthlyBudget * 0.8) {\n            alerts.push({\n                type: projectedMonthlyCost > monthlyBudget ? 'danger' : 'warning',\n                title: projectedMonthlyCost > monthlyBudget ? 'Budget Exceeded' : 'Budget Warning',\n                message: \"Projected monthly cost: \".concat(formatCurrency(projectedMonthlyCost), \" (Budget: \").concat(formatCurrency(monthlyBudget), \")\"),\n                value: \"\".concat((projectedMonthlyCost / monthlyBudget * 100).toFixed(0), \"%\")\n            });\n        }\n        // Success rate alert\n        if (summary.success_rate < 95) {\n            alerts.push({\n                type: summary.success_rate < 90 ? 'danger' : 'warning',\n                title: 'Low Success Rate',\n                message: \"Current success rate is \".concat(summary.success_rate.toFixed(1), \"%. Consider reviewing failed requests.\"),\n                value: \"\".concat(summary.success_rate.toFixed(1), \"%\")\n            });\n        }\n        // High cost per request alert\n        if (summary.average_cost_per_request > 0.01) {\n            alerts.push({\n                type: 'warning',\n                title: 'High Cost Per Request',\n                message: \"Average cost per request is \".concat(formatCurrency(summary.average_cost_per_request), \". Consider optimizing model usage.\"),\n                value: formatCurrency(summary.average_cost_per_request)\n            });\n        }\n        return alerts;\n    };\n    const generateRecommendations = ()=>{\n        const recommendations = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        const providers = (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data) || [];\n        const models = (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data) || [];\n        if (!summary) return recommendations;\n        // Cost optimization recommendations\n        const mostExpensiveModel = models.sort((a, b)=>b.cost - a.cost)[0];\n        if (mostExpensiveModel && mostExpensiveModel.cost > summary.total_cost * 0.3) {\n            const potentialSavings = mostExpensiveModel.cost * 0.2; // Assume 20% savings possible\n            recommendations.push({\n                type: 'cost_optimization',\n                title: 'Optimize Expensive Model Usage',\n                description: \"\".concat(mostExpensiveModel.name, \" accounts for \").concat((mostExpensiveModel.cost / summary.total_cost * 100).toFixed(1), \"% of your costs. Consider using a more cost-effective alternative for simpler tasks.\"),\n                potential_savings: potentialSavings,\n                icon: _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n            });\n        }\n        // Performance recommendations\n        if (summary.success_rate < 98) {\n            recommendations.push({\n                type: 'performance',\n                title: 'Improve Request Reliability',\n                description: \"Your success rate is \".concat(summary.success_rate.toFixed(1), \"%. Implement retry logic and error handling to improve reliability.\"),\n                icon: _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            });\n        }\n        // Efficiency recommendations\n        const avgTokensPerRequest = (summary.total_input_tokens + summary.total_output_tokens) / summary.total_requests;\n        if (avgTokensPerRequest > 1000) {\n            recommendations.push({\n                type: 'efficiency',\n                title: 'Optimize Token Usage',\n                description: \"Average \".concat(formatNumber(avgTokensPerRequest), \" tokens per request. Consider breaking down large prompts or using more efficient models.\"),\n                icon: _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n            });\n        }\n        return recommendations;\n    };\n    const resetFilters = ()=>{\n        setTimeRange('30');\n        setSelectedConfig('');\n        setStartDate('');\n        setEndDate('');\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 313,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-6\",\n                    children: \"\\uD83D\\uDCCA Advanced Analytics\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: [\n                                \"Error loading analytics: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"btn-primary\",\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 333,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const costEfficiency = summary ? summary.total_cost / Math.max(summary.successful_requests, 1) : 0;\n    const projectedMonthlyCost = summary ? summary.total_cost / parseInt(timeRange) * 30 : 0;\n    // Generate insights\n    const costAlerts = generateCostAlerts();\n    const recommendations = generateRecommendations();\n    // Calculate trends vs previous period\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : null;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : null;\n    // Professional Line Chart Component (like reference)\n    const ProfessionalLineChart = (param)=>{\n        let { data } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, this);\n        }\n        const maxCost = Math.max(...data.map((d)=>d.cost));\n        const minCost = Math.min(...data.map((d)=>d.cost));\n        const maxRequests = Math.max(...data.map((d)=>d.requests));\n        const minRequests = Math.min(...data.map((d)=>d.requests));\n        const costRange = maxCost - minCost || 1;\n        const requestRange = maxRequests - minRequests || 1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-64 w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-full h-full\",\n                viewBox: \"0 0 600 200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"costGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"requestGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                                id: \"grid\",\n                                width: \"40\",\n                                height: \"40\",\n                                patternUnits: \"userSpaceOnUse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 40 0 L 0 0 0 40\",\n                                    fill: \"none\",\n                                    stroke: \"#f1f5f9\",\n                                    strokeWidth: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"600\",\n                        height: \"200\",\n                        fill: \"url(#grid)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#costGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#requestGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#8b5cf6\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#06b6d4\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 11\n                    }, this),\n                    data.map((d, i)=>{\n                        const x = i / Math.max(data.length - 1, 1) * 600;\n                        const costY = 200 - (d.cost - minCost) / costRange * 160;\n                        const requestY = 200 - (d.requests - minRequests) / requestRange * 160;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: costY,\n                                    r: \"3\",\n                                    fill: \"#8b5cf6\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatCurrency(d.cost))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: requestY,\n                                    r: \"3\",\n                                    fill: \"#06b6d4\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatNumber(d.requests), \" requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 15\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 380,\n            columnNumber: 7\n        }, this);\n    };\n    // Professional Donut Chart Component (like reference)\n    const ProfessionalDonutChart = (param)=>{\n        let { data, total } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-48 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 491,\n                columnNumber: 9\n            }, this);\n        }\n        // Professional color palette like the reference\n        const colors = [\n            '#f43f5e',\n            '#8b5cf6',\n            '#06b6d4',\n            '#10b981',\n            '#f59e0b',\n            '#ef4444'\n        ];\n        const radius = 70;\n        const strokeWidth = 16;\n        const normalizedRadius = radius - strokeWidth * 0.5;\n        const circumference = normalizedRadius * 2 * Math.PI;\n        let cumulativePercentage = 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"160\",\n                            height: \"160\",\n                            className: \"transform -rotate-90\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"80\",\n                                    cy: \"80\",\n                                    r: normalizedRadius,\n                                    stroke: \"#f8fafc\",\n                                    strokeWidth: strokeWidth,\n                                    fill: \"transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                data.map((item, index)=>{\n                                    const percentage = item.cost / total * 100;\n                                    const strokeDasharray = \"\".concat(percentage / 100 * circumference, \" \").concat(circumference);\n                                    const strokeDashoffset = -(cumulativePercentage / 100 * circumference);\n                                    cumulativePercentage += percentage;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"80\",\n                                        cy: \"80\",\n                                        r: normalizedRadius,\n                                        stroke: colors[index % colors.length],\n                                        strokeWidth: strokeWidth,\n                                        strokeDasharray: strokeDasharray,\n                                        strokeDashoffset: strokeDashoffset,\n                                        fill: \"transparent\",\n                                        strokeLinecap: \"round\",\n                                        className: \"transition-all duration-300 hover:opacity-80 cursor-pointer\",\n                                        style: {\n                                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                        }\n                                    }, index, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: data.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Providers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 w-full\",\n                    children: data.slice(0, 4).map((item, index)=>{\n                        const percentage = (item.cost / total * 100).toFixed(1);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between py-2 px-3 rounded-lg hover:bg-white/5 transition-colors duration-150\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mr-3 shadow-sm\",\n                                            style: {\n                                                backgroundColor: colors[index]\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-white capitalize\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-semibold text-white\",\n                                            children: [\n                                                percentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: formatCurrency(item.cost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#040716] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-4 py-2 bg-cyan-500 text-white rounded-lg text-sm font-medium\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-4 py-2 text-gray-400 hover:text-white text-sm font-medium\",\n                                    children: \"Users\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-4 py-2 text-gray-400 hover:text-white text-sm font-medium\",\n                                    children: \"Errors\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-4 py-2 text-gray-400 hover:text-white text-sm font-medium\",\n                                    children: \"Cache\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-4 py-2 text-gray-400 hover:text-white text-sm font-medium\",\n                                    children: \"Feedback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-4 py-2 text-gray-400 hover:text-white text-sm font-medium\",\n                                    children: \"Metadata\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search Filter\",\n                                                className: \"bg-[#1B1C1D] border border-gray-700 text-white rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-cyan-500 w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeRange,\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        className: \"bg-[#1B1C1D] border border-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"7\",\n                                                children: \"2023-07-06 - Dec 2023\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"30\",\n                                                children: \"Last 30 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"90\",\n                                                children: \"Last 90 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, this),\n                summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Total Request Made\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: formatNumber(summary.total_requests)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    requestTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(requestTrend.isPositive ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'),\n                                                        children: [\n                                                            requestTrend.isPositive ? '+' : '',\n                                                            requestTrend.percentage.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Average Latency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: (summary === null || summary === void 0 ? void 0 : summary.average_latency) ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '144ms'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400\",\n                                                        children: \"+20.34%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"User Feedback\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: [\n                                                            summary.success_rate.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(summary.success_rate >= 95 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'),\n                                                        children: summary.success_rate >= 95 ? '+1.34%' : '-1.34%'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Cost\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: formatCurrency(summary.total_cost)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(costTrend.isPositive ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'),\n                                                        children: [\n                                                            costTrend.isPositive ? '+' : '',\n                                                            costTrend.percentage.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Cost\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white mr-3\",\n                                                            children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 98492)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-red-500/20 text-red-400\",\n                                                            children: \"-10.46%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-gray-700 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-4 w-4 text-gray-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-32 flex items-end space-x-1\",\n                                    children: [\n                                        ...Array(20)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500 rounded-t\",\n                                            style: {\n                                                height: \"\".concat(Math.random() * 80 + 20, \"%\"),\n                                                width: '4%'\n                                            }\n                                        }, i, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 742,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Latency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white mr-3\",\n                                                            children: (summary === null || summary === void 0 ? void 0 : summary.average_latency) ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '29ms'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400\",\n                                                            children: \"112%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-gray-700 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-32 relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-full h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M0,80 Q50,60 100,70 T200,50 T300,60\",\n                                            stroke: \"#f59e0b\",\n                                            strokeWidth: \"2\",\n                                            fill: \"none\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Tokens Used\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"March 28\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white mr-3\",\n                                                            children: summary ? \"\".concat(((summary.total_input_tokens + summary.total_output_tokens) / 1000000).toFixed(1), \"M\") : '25.4M'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400\",\n                                                            children: \"+9.39%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-gray-700 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-32 relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-full h-full\",\n                                        children: [\n                                            ...Array(30)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: Math.random() * 280 + 10,\n                                                cy: Math.random() * 100 + 10,\n                                                r: \"2\",\n                                                fill: [\n                                                    '#10b981',\n                                                    '#3b82f6',\n                                                    '#f59e0b'\n                                                ][Math.floor(Math.random() * 3)]\n                                            }, i, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 808,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white mr-3\",\n                                                            children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 63800)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 848,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400\",\n                                                            children: \"+3.39%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-gray-700 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 857,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        ...Array(6)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-pink-500 to-green-500 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(Math.random() * 80 + 20, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, i, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 863,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1B1C1D] border border-gray-700 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: \"Unique Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white mr-3\",\n                                                            children: \"78\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400\",\n                                                            children: \"+3.39%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-gray-700 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-4 w-4 text-gray-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 887,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 877,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-32 relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-full h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M0,80 Q50,40 100,60 T200,50 T300,70\",\n                                            stroke: \"#8b5cf6\",\n                                            strokeWidth: \"2\",\n                                            fill: \"none\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 876,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 740,\n                    columnNumber: 9\n                }, this),\n                summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Total Request Made\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: formatNumber(summary.total_requests)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    requestTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(requestTrend.isPositive ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'),\n                                                        children: [\n                                                            requestTrend.isPositive ? '+' : '',\n                                                            requestTrend.percentage.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 915,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 912,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 911,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Average Latency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: summary.average_latency ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '0ms'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400\",\n                                                        children: \"+20.34%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 949,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"User Feedback\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: [\n                                                            summary.success_rate.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(summary.success_rate >= 95 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'),\n                                                        children: summary.success_rate >= 95 ? '+1.34%' : '-1.34%'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Total Cost\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: formatCurrency(summary.total_cost)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(costTrend.isPositive ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'),\n                                                        children: [\n                                                            costTrend.isPositive ? '+' : '',\n                                                            costTrend.percentage.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 978,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 977,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 909,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Cost\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-white mr-3\",\n                                                    children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1009,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-red-500/20 text-red-400\",\n                                                    children: \"10.46%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"This period\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1020,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1021,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500 h-2 rounded-full\",\n                                                style: {\n                                                    width: '75%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1024,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1018,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1005,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Latency\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-white mr-3\",\n                                                    children: (summary === null || summary === void 0 ? void 0 : summary.average_latency) ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '0ms'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1034,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400\",\n                                                    children: \"112%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1037,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Average response\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: (summary === null || summary === void 0 ? void 0 : summary.average_latency) ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '0ms'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1046,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-500 h-2 rounded-full\",\n                                                style: {\n                                                    width: '60%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1049,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1048,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1030,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Tokens Used\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-white mr-3\",\n                                                    children: summary ? \"\".concat(((summary.total_input_tokens + summary.total_output_tokens) / 1000000).toFixed(1), \"M\") : '0M'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1059,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400\",\n                                                    children: \"+9.39%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1062,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1058,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1056,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Total processed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1070,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: summary ? formatNumber(summary.total_input_tokens + summary.total_output_tokens) : '0'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1071,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-500 h-2 rounded-full\",\n                                                style: {\n                                                    width: '85%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1073,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1055,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1003,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-1\",\n                                            children: \"Provider Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Detailed breakdown by provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1084,\n                                    columnNumber: 13\n                                }, this),\n                                (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-white/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Provider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1093,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1095,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Share\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1096,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1092,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1091,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-white/5\",\n                                                children: analyticsData.grouped_data.sort((a, b)=>b.cost - a.cost).map((provider, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444',\n                                                        '#8b5cf6'\n                                                    ];\n                                                    const percentage = (provider.cost / ((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 1) * 100).toFixed(1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-white/5 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1109,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-white capitalize\",\n                                                                            children: provider.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1113,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1108,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1107,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-white\",\n                                                                children: formatCurrency(provider.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-400\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1119,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/10 text-gray-300\",\n                                                                    children: [\n                                                                        percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1123,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1122,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1090,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1089,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No provider data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1136,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1083,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-1\",\n                                            children: \"Top Models\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Most expensive models by cost\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1143,\n                                    columnNumber: 13\n                                }, this),\n                                (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-white/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Model\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1153,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1154,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Avg/Request\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1156,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1152,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-white/5\",\n                                                children: modelAnalytics.grouped_data.sort((a, b)=>b.cost - a.cost).slice(0, 5).map((model, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444'\n                                                    ];\n                                                    const avgCost = model.cost / Math.max(model.requests, 1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-white/5 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1170,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-white\",\n                                                                                    children: model.name.length > 20 ? model.name.substring(0, 20) + '...' : model.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 1175,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                                                    children: [\n                                                                                        formatNumber(model.input_tokens + model.output_tokens),\n                                                                                        \" tokens\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 1176,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1174,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1169,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-white\",\n                                                                children: formatCurrency(model.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1182,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-300\",\n                                                                children: formatNumber(model.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-300\",\n                                                                children: formatCurrency(avgCost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1188,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, model.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1167,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1159,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No model data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1200,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1198,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1081,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 588,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 587,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"qgClNvt3fYHqAHTRr8MfhVCyyr4=\");\n_c = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1223,\n                            columnNumber: 11\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1224,\n                            columnNumber: 11\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1222,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1229,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1230,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1231,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1228,\n                            columnNumber: 13\n                        }, void 0))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1226,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1221,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1237,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 1220,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AnalyticsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AnalyticsPageContent\");\n$RefreshReg$(_c1, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYW5hbHl0aWNzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1FO0FBbUI5QjtBQXlEckMsU0FBU1k7O0lBQ1AsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR2QsK0NBQVFBLENBQXVCO0lBQ3pFLE1BQU0sQ0FBQ2UsZ0JBQWdCQyxrQkFBa0IsR0FBR2hCLCtDQUFRQSxDQUF1QjtJQUMzRSxNQUFNLENBQUNpQixpQkFBaUJDLG1CQUFtQixHQUFHbEIsK0NBQVFBLENBQXVCO0lBQzdFLE1BQU0sQ0FBQ21CLGdCQUFnQkMsa0JBQWtCLEdBQUdwQiwrQ0FBUUEsQ0FBbUIsRUFBRTtJQUN6RSxNQUFNLENBQUNxQixvQkFBb0JDLHNCQUFzQixHQUFHdEIsK0NBQVFBLENBQXVCO0lBQ25GLE1BQU0sQ0FBQ3VCLGVBQWVDLGlCQUFpQixHQUFHeEIsK0NBQVFBLENBQW9CLEVBQUU7SUFDeEUsTUFBTSxDQUFDeUIsU0FBU0MsV0FBVyxHQUFHMUIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDMkIsT0FBT0MsU0FBUyxHQUFHNUIsK0NBQVFBLENBQWdCO0lBRWxELFVBQVU7SUFDVixNQUFNLENBQUM2QixXQUFXQyxhQUFhLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMrQixnQkFBZ0JDLGtCQUFrQixHQUFHaEMsK0NBQVFBLENBQVM7SUFDN0QsTUFBTSxDQUFDaUMsYUFBYUMsZUFBZSxHQUFHbEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDbUMsV0FBV0MsYUFBYSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDcUMsU0FBU0MsV0FBVyxHQUFHdEMsK0NBQVFBLENBQUM7SUFFdkMsa0JBQWtCO0lBQ2xCLE1BQU0sQ0FBQ3VDLGVBQWVDLGlCQUFpQixHQUFHeEMsK0NBQVFBLENBQVMsTUFBTSxxQkFBcUI7SUFDdEYsTUFBTSxDQUFDeUMsb0JBQW9CQyxzQkFBc0IsR0FBRzFDLCtDQUFRQSxDQUFDO0lBRTdEQyxnREFBU0E7MENBQUM7WUFDUjBDO1FBQ0Y7eUNBQUcsRUFBRTtJQUVMLE1BQU1BLHFCQUFxQjtRQUN6QixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUlELFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxVQUFVLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ25DeEIsaUJBQWlCdUI7WUFDbkI7UUFDRixFQUFFLE9BQU9FLEtBQUs7WUFDWkMsUUFBUXZCLEtBQUssQ0FBQywyQkFBMkJzQjtRQUMzQztJQUNGO0lBRUEsTUFBTUUscUJBQXFCakQsa0RBQVdBO2dFQUFDO1lBQ3JDLElBQUk7Z0JBQ0Z3QixXQUFXO2dCQUNYRSxTQUFTO2dCQUVULDRDQUE0QztnQkFDNUMsTUFBTXdCLFNBQVMsSUFBSUM7Z0JBQ25CLElBQUlDO2dCQUNKLElBQUlDLGlCQUF1QixJQUFJQztnQkFFL0IsSUFBSXJCLGFBQWFFLFNBQVM7b0JBQ3hCaUIsbUJBQW1CLElBQUlFLEtBQUtyQjtvQkFDNUJvQixpQkFBaUIsSUFBSUMsS0FBS25CO29CQUMxQmUsT0FBT0ssTUFBTSxDQUFDLGFBQWFILGlCQUFpQkksV0FBVztvQkFDdkROLE9BQU9LLE1BQU0sQ0FBQyxXQUFXRixlQUFlRyxXQUFXO2dCQUNyRCxPQUFPLElBQUk3QixXQUFXO29CQUNwQnlCLG1CQUFtQixJQUFJRTtvQkFDdkJGLGlCQUFpQkssT0FBTyxDQUFDTCxpQkFBaUJNLE9BQU8sS0FBS0MsU0FBU2hDO29CQUMvRHVCLE9BQU9LLE1BQU0sQ0FBQyxhQUFhSCxpQkFBaUJJLFdBQVc7Z0JBQ3pEO2dCQUVBLElBQUkzQixnQkFBZ0I7b0JBQ2xCcUIsT0FBT0ssTUFBTSxDQUFDLHFCQUFxQjFCO2dCQUNyQztnQkFFQSw4REFBOEQ7Z0JBQzlELE1BQU0rQixhQUFhLElBQUlUO2dCQUN2QixNQUFNVSxlQUFlUixlQUFlUyxPQUFPLEtBQUtWLGlCQUFrQlUsT0FBTztnQkFDekUsTUFBTUMsZ0JBQWdCLElBQUlULEtBQUtGLGlCQUFrQlUsT0FBTyxLQUFLRDtnQkFDN0QsTUFBTUcsY0FBYyxJQUFJVixLQUFLRixpQkFBa0JVLE9BQU87Z0JBRXRERixXQUFXTCxNQUFNLENBQUMsYUFBYVEsY0FBY1AsV0FBVztnQkFDeERJLFdBQVdMLE1BQU0sQ0FBQyxXQUFXUyxZQUFZUixXQUFXO2dCQUNwRCxJQUFJM0IsZ0JBQWdCO29CQUNsQitCLFdBQVdMLE1BQU0sQ0FBQyxxQkFBcUIxQjtnQkFDekM7Z0JBRUEsaUNBQWlDO2dCQUNqQyxNQUFNLENBQ0pvQyxrQkFDQUMsZUFDQUMsb0JBQ0FDLHVCQUNELEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO29CQUNwQjNCLE1BQU0sMEJBQTRDLE9BQWxCTyxPQUFPcUIsUUFBUSxJQUFHO29CQUNsRDVCLE1BQU0sMEJBQTRDLE9BQWxCTyxPQUFPcUIsUUFBUSxJQUFHO29CQUNsRDVCLE1BQU0sMEJBQTRDLE9BQWxCTyxPQUFPcUIsUUFBUSxJQUFHO29CQUNsRDVCLE1BQU0sMEJBQWdELE9BQXRCaUIsV0FBV1csUUFBUSxJQUFHO2lCQUN2RDtnQkFFRCxJQUFJLENBQUNOLGlCQUFpQnJCLEVBQUUsSUFBSSxDQUFDc0IsY0FBY3RCLEVBQUUsSUFBSSxDQUFDdUIsbUJBQW1CdkIsRUFBRSxFQUFFO29CQUN2RSxNQUFNLElBQUk0QixNQUFNO2dCQUNsQjtnQkFFQSxNQUFNQyxlQUFlLE1BQU1SLGlCQUFpQm5CLElBQUk7Z0JBQ2hELE1BQU00QixZQUFZLE1BQU1SLGNBQWNwQixJQUFJO2dCQUMxQyxNQUFNNkIsbUJBQW1CLE1BQU1SLG1CQUFtQnJCLElBQUk7Z0JBQ3RELE1BQU04Qix1QkFBdUJSLHVCQUF1QnhCLEVBQUUsR0FBRyxNQUFNd0IsdUJBQXVCdEIsSUFBSSxLQUFLO2dCQUUvRmxDLGlCQUFpQjZEO2dCQUNqQjNELGtCQUFrQjREO2dCQUNsQnRELHNCQUFzQndEO2dCQUV0QiwyQkFBMkI7Z0JBQzNCLE1BQU1DLHNCQUF3Q0YsaUJBQWlCRyxZQUFZLENBQUNDLEdBQUc7Z0dBQUMsQ0FBQ0MsT0FBZTs0QkFDOUZDLFFBQVFELEtBQUtDLE1BQU07NEJBQ25CQyxNQUFNRixLQUFLRSxJQUFJOzRCQUNmQyxVQUFVSCxLQUFLRyxRQUFROzRCQUN2QkMsUUFBUUosS0FBS0ssWUFBWSxHQUFHTCxLQUFLTSxhQUFhO3dCQUNoRDs7Z0JBQ0FwRSxrQkFBa0IyRDtZQUVwQixFQUFFLE9BQU85QixLQUFVO2dCQUNqQnJCLFNBQVNxQixJQUFJd0MsT0FBTztnQkFDcEJ2QyxRQUFRdkIsS0FBSyxDQUFDLDZCQUE2QnNCO1lBQzdDLFNBQVU7Z0JBQ1J2QixXQUFXO1lBQ2I7UUFDRjsrREFBRztRQUFDRztRQUFXRTtRQUFnQkk7UUFBV0U7S0FBUTtJQUVsRHBDLGdEQUFTQTswQ0FBQztZQUNSa0Q7UUFDRjt5Q0FBRztRQUFDQTtRQUFvQnRCO1FBQVdFO1FBQWdCSTtRQUFXRTtLQUFRO0lBRXRFLE1BQU1xRCxpQkFBaUIsQ0FBQ0M7UUFDdEIsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUMsU0FBUztZQUNwQ0MsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLHVCQUF1QjtZQUN2QkMsdUJBQXVCO1FBQ3pCLEdBQUdDLE1BQU0sQ0FBQ1A7SUFDWjtJQUVBLE1BQU1RLGVBQWUsQ0FBQ0M7UUFDcEIsT0FBTyxJQUFJUixLQUFLQyxZQUFZLENBQUMsU0FBU0ssTUFBTSxDQUFDRTtJQUMvQztJQUVBLE1BQU1DLGlCQUFpQixDQUFDQyxTQUFpQkM7UUFDdkMsSUFBSUEsYUFBYSxHQUFHLE9BQU87WUFBRUMsWUFBWTtZQUFHQyxZQUFZO1FBQUs7UUFDN0QsTUFBTUQsYUFBYSxDQUFFRixVQUFVQyxRQUFPLElBQUtBLFdBQVk7UUFDdkQsT0FBTztZQUFFQyxZQUFZRSxLQUFLQyxHQUFHLENBQUNIO1lBQWFDLFlBQVlELGNBQWM7UUFBRTtJQUN6RTtJQUVBLE1BQU1JLHFCQUFxQjtRQUN6QixNQUFNQyxTQUFzQixFQUFFO1FBQzlCLE1BQU1DLFVBQVVqRywwQkFBQUEsb0NBQUFBLGNBQWVpRyxPQUFPO1FBRXRDLElBQUksQ0FBQ0EsU0FBUyxPQUFPRDtRQUVyQixlQUFlO1FBQ2YsTUFBTUUsdUJBQXVCLFFBQVNDLFVBQVUsR0FBR25ELFNBQVNoQyxhQUFjO1FBQzFFLElBQUlrRix1QkFBdUJ4RSxnQkFBZ0IsS0FBSztZQUM5Q3NFLE9BQU9JLElBQUksQ0FBQztnQkFDVkMsTUFBTUgsdUJBQXVCeEUsZ0JBQWdCLFdBQVc7Z0JBQ3hENEUsT0FBT0osdUJBQXVCeEUsZ0JBQWdCLG9CQUFvQjtnQkFDbEVrRCxTQUFTLDJCQUE0RUMsT0FBakRBLGVBQWVxQix1QkFBc0IsY0FBMEMsT0FBOUJyQixlQUFlbkQsZ0JBQWU7Z0JBQ25INkUsT0FBTyxHQUE2RCxPQUExRCxDQUFDLHVCQUF3QjdFLGdCQUFpQixHQUFFLEVBQUc4RSxPQUFPLENBQUMsSUFBRztZQUN0RTtRQUNGO1FBRUEscUJBQXFCO1FBQ3JCLElBQUlQLFFBQVFRLFlBQVksR0FBRyxJQUFJO1lBQzdCVCxPQUFPSSxJQUFJLENBQUM7Z0JBQ1ZDLE1BQU1KLFFBQVFRLFlBQVksR0FBRyxLQUFLLFdBQVc7Z0JBQzdDSCxPQUFPO2dCQUNQMUIsU0FBUywyQkFBMkQsT0FBaENxQixRQUFRUSxZQUFZLENBQUNELE9BQU8sQ0FBQyxJQUFHO2dCQUNwRUQsT0FBTyxHQUFtQyxPQUFoQ04sUUFBUVEsWUFBWSxDQUFDRCxPQUFPLENBQUMsSUFBRztZQUM1QztRQUNGO1FBRUEsOEJBQThCO1FBQzlCLElBQUlQLFFBQVFTLHdCQUF3QixHQUFHLE1BQU07WUFDM0NWLE9BQU9JLElBQUksQ0FBQztnQkFDVkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUDFCLFNBQVMsK0JBQWdGLE9BQWpEQyxlQUFlb0IsUUFBUVMsd0JBQXdCLEdBQUU7Z0JBQ3pGSCxPQUFPMUIsZUFBZW9CLFFBQVFTLHdCQUF3QjtZQUN4RDtRQUNGO1FBRUEsT0FBT1Y7SUFDVDtJQUVBLE1BQU1XLDBCQUEwQjtRQUM5QixNQUFNQyxrQkFBeUMsRUFBRTtRQUNqRCxNQUFNWCxVQUFVakcsMEJBQUFBLG9DQUFBQSxjQUFlaUcsT0FBTztRQUN0QyxNQUFNWSxZQUFZN0csQ0FBQUEsMEJBQUFBLG9DQUFBQSxjQUFlbUUsWUFBWSxLQUFJLEVBQUU7UUFDbkQsTUFBTTJDLFNBQVM1RyxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCaUUsWUFBWSxLQUFJLEVBQUU7UUFFakQsSUFBSSxDQUFDOEIsU0FBUyxPQUFPVztRQUVyQixvQ0FBb0M7UUFDcEMsTUFBTUcscUJBQXFCRCxPQUFPRSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRTNDLElBQUksR0FBRzBDLEVBQUUxQyxJQUFJLENBQUMsQ0FBQyxFQUFFO1FBQ3BFLElBQUl3QyxzQkFBc0JBLG1CQUFtQnhDLElBQUksR0FBRzBCLFFBQVFFLFVBQVUsR0FBRyxLQUFLO1lBQzVFLE1BQU1nQixtQkFBbUJKLG1CQUFtQnhDLElBQUksR0FBRyxLQUFLLDhCQUE4QjtZQUN0RnFDLGdCQUFnQlIsSUFBSSxDQUFDO2dCQUNuQkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUGMsYUFBYSxHQUEyQyxPQUF4Q0wsbUJBQW1CTSxJQUFJLEVBQUMsa0JBQWtGLE9BQWxFLENBQUMsbUJBQW9COUMsSUFBSSxHQUFHMEIsUUFBUUUsVUFBVSxHQUFJLEdBQUUsRUFBR0ssT0FBTyxDQUFDLElBQUc7Z0JBQzFIYyxtQkFBbUJIO2dCQUNuQkksTUFBTS9ILDBNQUFrQkE7WUFDMUI7UUFDRjtRQUVBLDhCQUE4QjtRQUM5QixJQUFJeUcsUUFBUVEsWUFBWSxHQUFHLElBQUk7WUFDN0JHLGdCQUFnQlIsSUFBSSxDQUFDO2dCQUNuQkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUGMsYUFBYSx3QkFBd0QsT0FBaENuQixRQUFRUSxZQUFZLENBQUNELE9BQU8sQ0FBQyxJQUFHO2dCQUNyRWUsTUFBTTVILDBNQUFlQTtZQUN2QjtRQUNGO1FBRUEsNkJBQTZCO1FBQzdCLE1BQU02SCxzQkFBc0IsQ0FBQ3ZCLFFBQVF3QixrQkFBa0IsR0FBR3hCLFFBQVF5QixtQkFBbUIsSUFBSXpCLFFBQVEwQixjQUFjO1FBQy9HLElBQUlILHNCQUFzQixNQUFNO1lBQzlCWixnQkFBZ0JSLElBQUksQ0FBQztnQkFDbkJDLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BjLGFBQWEsV0FBNkMsT0FBbEM5QixhQUFha0Msc0JBQXFCO2dCQUMxREQsTUFBTTdILDBNQUFXQTtZQUNuQjtRQUNGO1FBRUEsT0FBT2tIO0lBQ1Q7SUFFQSxNQUFNZ0IsZUFBZTtRQUNuQjNHLGFBQWE7UUFDYkUsa0JBQWtCO1FBQ2xCSSxhQUFhO1FBQ2JFLFdBQVc7SUFDYjtJQUVBLElBQUliLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2lIO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OEJBRWpCLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDWjsyQkFBSUMsTUFBTTtxQkFBRyxDQUFDM0QsR0FBRyxDQUFDLENBQUM0RCxHQUFHQyxrQkFDckIsOERBQUNKOzRCQUFZQyxXQUFVOzs4Q0FDckIsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OzJCQUhQRzs7Ozs7Ozs7Ozs7Ozs7OztJQVNwQjtJQUVBLElBQUluSCxPQUFPO1FBQ1QscUJBQ0UsOERBQUMrRztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0k7b0JBQUdKLFdBQVU7OEJBQTBCOzs7Ozs7OEJBQ3hDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNLOzRCQUFFTCxXQUFVOztnQ0FBb0I7Z0NBQTBCaEg7Ozs7Ozs7c0NBQzNELDhEQUFDc0g7NEJBQU9DLFNBQVMvRjs0QkFBb0J3RixXQUFVO3NDQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNckU7SUFFQSxNQUFNN0IsVUFBVWpHLDBCQUFBQSxvQ0FBQUEsY0FBZWlHLE9BQU87SUFDdEMsTUFBTXFDLGlCQUFpQnJDLFVBQVVBLFFBQVFFLFVBQVUsR0FBR04sS0FBSzBDLEdBQUcsQ0FBQ3RDLFFBQVF1QyxtQkFBbUIsRUFBRSxLQUFLO0lBQ2pHLE1BQU10Qyx1QkFBdUJELFVBQVUsUUFBU0UsVUFBVSxHQUFHbkQsU0FBU2hDLGFBQWMsS0FBSztJQUV6RixvQkFBb0I7SUFDcEIsTUFBTXlILGFBQWExQztJQUNuQixNQUFNYSxrQkFBa0JEO0lBRXhCLHNDQUFzQztJQUN0QyxNQUFNK0Isa0JBQWtCbEksK0JBQUFBLHlDQUFBQSxtQkFBb0J5RixPQUFPO0lBQ25ELE1BQU0wQyxZQUFZRCxrQkFBa0JsRCxlQUFlUyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNFLFVBQVUsS0FBSSxHQUFHdUMsZ0JBQWdCdkMsVUFBVSxJQUFJO0lBQzNHLE1BQU15QyxlQUFlRixrQkFBa0JsRCxlQUFlUyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMwQixjQUFjLEtBQUksR0FBR2UsZ0JBQWdCZixjQUFjLElBQUk7SUFFdEgscURBQXFEO0lBQ3JELE1BQU1rQix3QkFBd0I7WUFBQyxFQUFFQyxJQUFJLEVBQThCO1FBQ2pFLElBQUksQ0FBQ0EsS0FBS0MsTUFBTSxFQUFFO1lBQ2hCLHFCQUNFLDhEQUFDbEI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3ZJLDBNQUFZQTs0QkFBQ3VJLFdBQVU7Ozs7OztzQ0FDeEIsOERBQUNLOzRCQUFFTCxXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJckM7UUFFQSxNQUFNa0IsVUFBVW5ELEtBQUswQyxHQUFHLElBQUlPLEtBQUsxRSxHQUFHLENBQUM2RSxDQUFBQSxJQUFLQSxFQUFFMUUsSUFBSTtRQUNoRCxNQUFNMkUsVUFBVXJELEtBQUtzRCxHQUFHLElBQUlMLEtBQUsxRSxHQUFHLENBQUM2RSxDQUFBQSxJQUFLQSxFQUFFMUUsSUFBSTtRQUNoRCxNQUFNNkUsY0FBY3ZELEtBQUswQyxHQUFHLElBQUlPLEtBQUsxRSxHQUFHLENBQUM2RSxDQUFBQSxJQUFLQSxFQUFFekUsUUFBUTtRQUN4RCxNQUFNNkUsY0FBY3hELEtBQUtzRCxHQUFHLElBQUlMLEtBQUsxRSxHQUFHLENBQUM2RSxDQUFBQSxJQUFLQSxFQUFFekUsUUFBUTtRQUV4RCxNQUFNOEUsWUFBWU4sVUFBVUUsV0FBVztRQUN2QyxNQUFNSyxlQUFlSCxjQUFjQyxlQUFlO1FBRWxELHFCQUNFLDhEQUFDeEI7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQzBCO2dCQUFJMUIsV0FBVTtnQkFBZ0IyQixTQUFROztrQ0FDckMsOERBQUNDOzswQ0FFQyw4REFBQ0M7Z0NBQWVDLElBQUc7Z0NBQWVDLElBQUc7Z0NBQUtDLElBQUc7Z0NBQUtDLElBQUc7Z0NBQUtDLElBQUc7O2tEQUMzRCw4REFBQ0M7d0NBQUtDLFFBQU87d0NBQUtDLFdBQVU7d0NBQVVDLGFBQVk7Ozs7OztrREFDbEQsOERBQUNIO3dDQUFLQyxRQUFPO3dDQUFPQyxXQUFVO3dDQUFVQyxhQUFZOzs7Ozs7Ozs7Ozs7MENBRXRELDhEQUFDVDtnQ0FBZUMsSUFBRztnQ0FBa0JDLElBQUc7Z0NBQUtDLElBQUc7Z0NBQUtDLElBQUc7Z0NBQUtDLElBQUc7O2tEQUM5RCw4REFBQ0M7d0NBQUtDLFFBQU87d0NBQUtDLFdBQVU7d0NBQVVDLGFBQVk7Ozs7OztrREFDbEQsOERBQUNIO3dDQUFLQyxRQUFPO3dDQUFPQyxXQUFVO3dDQUFVQyxhQUFZOzs7Ozs7Ozs7Ozs7MENBSXRELDhEQUFDQztnQ0FBUVQsSUFBRztnQ0FBT1UsT0FBTTtnQ0FBS0MsUUFBTztnQ0FBS0MsY0FBYTswQ0FDckQsNEVBQUNDO29DQUFLeEIsR0FBRTtvQ0FBb0J5QixNQUFLO29DQUFPQyxRQUFPO29DQUFVQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLekUsOERBQUNDO3dCQUFLUCxPQUFNO3dCQUFNQyxRQUFPO3dCQUFNRyxNQUFLOzs7Ozs7a0NBR3BDLDhEQUFDSTt3QkFDQ0osTUFBSzt3QkFDTEssUUFBUSxTQUlLLE9BSklqQyxLQUFLMUUsR0FBRyxDQUFDLENBQUM2RSxHQUFHaEI7NEJBQzVCLE1BQU0rQyxJQUFJLElBQUtuRixLQUFLMEMsR0FBRyxDQUFDTyxLQUFLQyxNQUFNLEdBQUcsR0FBRyxLQUFNOzRCQUMvQyxNQUFNa0MsSUFBSSxNQUFNLENBQUVoQyxFQUFFMUUsSUFBSSxHQUFHMkUsT0FBTSxJQUFLSSxZQUFhOzRCQUNuRCxPQUFPLEdBQVEyQixPQUFMRCxHQUFFLEtBQUssT0FBRkM7d0JBQ2pCLEdBQUdDLElBQUksQ0FBQyxNQUFLOzs7Ozs7a0NBSWYsOERBQUNKO3dCQUNDSixNQUFLO3dCQUNMSyxRQUFRLFNBSUssT0FKSWpDLEtBQUsxRSxHQUFHLENBQUMsQ0FBQzZFLEdBQUdoQjs0QkFDNUIsTUFBTStDLElBQUksSUFBS25GLEtBQUswQyxHQUFHLENBQUNPLEtBQUtDLE1BQU0sR0FBRyxHQUFHLEtBQU07NEJBQy9DLE1BQU1rQyxJQUFJLE1BQU0sQ0FBRWhDLEVBQUV6RSxRQUFRLEdBQUc2RSxXQUFVLElBQUtFLGVBQWdCOzRCQUM5RCxPQUFPLEdBQVEwQixPQUFMRCxHQUFFLEtBQUssT0FBRkM7d0JBQ2pCLEdBQUdDLElBQUksQ0FBQyxNQUFLOzs7Ozs7a0NBSWYsOERBQUNDO3dCQUNDVCxNQUFLO3dCQUNMQyxRQUFPO3dCQUNQQyxhQUFZO3dCQUNaUSxlQUFjO3dCQUNkQyxnQkFBZTt3QkFDZk4sUUFBUWpDLEtBQUsxRSxHQUFHLENBQUMsQ0FBQzZFLEdBQUdoQjs0QkFDbkIsTUFBTStDLElBQUksSUFBS25GLEtBQUswQyxHQUFHLENBQUNPLEtBQUtDLE1BQU0sR0FBRyxHQUFHLEtBQU07NEJBQy9DLE1BQU1rQyxJQUFJLE1BQU0sQ0FBRWhDLEVBQUUxRSxJQUFJLEdBQUcyRSxPQUFNLElBQUtJLFlBQWE7NEJBQ25ELE9BQU8sR0FBUTJCLE9BQUxELEdBQUUsS0FBSyxPQUFGQzt3QkFDakIsR0FBR0MsSUFBSSxDQUFDOzs7Ozs7a0NBSVYsOERBQUNDO3dCQUNDVCxNQUFLO3dCQUNMQyxRQUFPO3dCQUNQQyxhQUFZO3dCQUNaUSxlQUFjO3dCQUNkQyxnQkFBZTt3QkFDZk4sUUFBUWpDLEtBQUsxRSxHQUFHLENBQUMsQ0FBQzZFLEdBQUdoQjs0QkFDbkIsTUFBTStDLElBQUksSUFBS25GLEtBQUswQyxHQUFHLENBQUNPLEtBQUtDLE1BQU0sR0FBRyxHQUFHLEtBQU07NEJBQy9DLE1BQU1rQyxJQUFJLE1BQU0sQ0FBRWhDLEVBQUV6RSxRQUFRLEdBQUc2RSxXQUFVLElBQUtFLGVBQWdCOzRCQUM5RCxPQUFPLEdBQVEwQixPQUFMRCxHQUFFLEtBQUssT0FBRkM7d0JBQ2pCLEdBQUdDLElBQUksQ0FBQzs7Ozs7O29CQUlUcEMsS0FBSzFFLEdBQUcsQ0FBQyxDQUFDNkUsR0FBR2hCO3dCQUNaLE1BQU0rQyxJQUFJLElBQUtuRixLQUFLMEMsR0FBRyxDQUFDTyxLQUFLQyxNQUFNLEdBQUcsR0FBRyxLQUFNO3dCQUMvQyxNQUFNdUMsUUFBUSxNQUFNLENBQUVyQyxFQUFFMUUsSUFBSSxHQUFHMkUsT0FBTSxJQUFLSSxZQUFhO3dCQUN2RCxNQUFNaUMsV0FBVyxNQUFNLENBQUV0QyxFQUFFekUsUUFBUSxHQUFHNkUsV0FBVSxJQUFLRSxlQUFnQjt3QkFDckUscUJBQ0UsOERBQUNpQzs7OENBQ0MsOERBQUNDO29DQUNDQyxJQUFJVjtvQ0FDSlcsSUFBSUw7b0NBQ0pNLEdBQUU7b0NBQ0ZsQixNQUFLO29DQUNMQyxRQUFPO29DQUNQQyxhQUFZO29DQUNaOUMsV0FBVTs4Q0FFViw0RUFBQ3hCO2tEQUFPLEdBQWdCekIsT0FBYm9FLEVBQUUzRSxNQUFNLEVBQUMsTUFBMkIsT0FBdkJPLGVBQWVvRSxFQUFFMUUsSUFBSTs7Ozs7Ozs7Ozs7OENBRS9DLDhEQUFDa0g7b0NBQ0NDLElBQUlWO29DQUNKVyxJQUFJSjtvQ0FDSkssR0FBRTtvQ0FDRmxCLE1BQUs7b0NBQ0xDLFFBQU87b0NBQ1BDLGFBQVk7b0NBQ1o5QyxXQUFVOzhDQUVWLDRFQUFDeEI7a0RBQU8sR0FBZ0JoQixPQUFiMkQsRUFBRTNFLE1BQU0sRUFBQyxNQUE2QixPQUF6QmdCLGFBQWEyRCxFQUFFekUsUUFBUSxHQUFFOzs7Ozs7Ozs7Ozs7MkJBckI3Q3lEOzs7OztvQkF5Qlo7Ozs7Ozs7Ozs7OztJQUlSO0lBRUEsc0RBQXNEO0lBQ3RELE1BQU00RCx5QkFBeUI7WUFBQyxFQUFFL0MsSUFBSSxFQUFFZ0QsS0FBSyxFQUFrQztRQUM3RSxJQUFJLENBQUNoRCxLQUFLQyxNQUFNLEVBQUU7WUFDaEIscUJBQ0UsOERBQUNsQjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDbEksME1BQVlBOzRCQUFDa0ksV0FBVTs7Ozs7O3NDQUN4Qiw4REFBQ0s7NEJBQUVMLFdBQVU7c0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztRQUlyQztRQUVBLGdEQUFnRDtRQUNoRCxNQUFNaUUsU0FBUztZQUFDO1lBQVc7WUFBVztZQUFXO1lBQVc7WUFBVztTQUFVO1FBQ2pGLE1BQU1DLFNBQVM7UUFDZixNQUFNcEIsY0FBYztRQUNwQixNQUFNcUIsbUJBQW1CRCxTQUFTcEIsY0FBYztRQUNoRCxNQUFNc0IsZ0JBQWdCRCxtQkFBbUIsSUFBSXBHLEtBQUtzRyxFQUFFO1FBRXBELElBQUlDLHVCQUF1QjtRQUUzQixxQkFDRSw4REFBQ3ZFO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUMwQjs0QkFBSWMsT0FBTTs0QkFBTUMsUUFBTzs0QkFBTXpDLFdBQVU7OzhDQUV0Qyw4REFBQzJEO29DQUNDQyxJQUFHO29DQUNIQyxJQUFHO29DQUNIQyxHQUFHSztvQ0FDSHRCLFFBQU87b0NBQ1BDLGFBQWFBO29DQUNiRixNQUFLOzs7Ozs7Z0NBSU41QixLQUFLMUUsR0FBRyxDQUFDLENBQUNDLE1BQU1nSTtvQ0FDZixNQUFNMUcsYUFBYSxLQUFNcEIsSUFBSSxHQUFHdUgsUUFBUztvQ0FDekMsTUFBTVEsa0JBQWtCLEdBQXlDSixPQUF0QyxhQUFjLE1BQU9BLGVBQWMsS0FBaUIsT0FBZEE7b0NBQ2pFLE1BQU1LLG1CQUFtQixDQUFFLHdCQUF3QixNQUFPTCxhQUFZO29DQUV0RUUsd0JBQXdCekc7b0NBRXhCLHFCQUNFLDhEQUFDOEY7d0NBRUNDLElBQUc7d0NBQ0hDLElBQUc7d0NBQ0hDLEdBQUdLO3dDQUNIdEIsUUFBUW9CLE1BQU0sQ0FBQ00sUUFBUU4sT0FBT2hELE1BQU0sQ0FBQzt3Q0FDckM2QixhQUFhQTt3Q0FDYjBCLGlCQUFpQkE7d0NBQ2pCQyxrQkFBa0JBO3dDQUNsQjdCLE1BQUs7d0NBQ0xVLGVBQWM7d0NBQ2R0RCxXQUFVO3dDQUNWN0MsT0FBTzs0Q0FDTHVILFFBQVE7d0NBQ1Y7dUNBYktIOzs7OztnQ0FnQlg7Ozs7Ozs7c0NBSUYsOERBQUN4RTs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBb0NnQixLQUFLQyxNQUFNOzs7Ozs7a0RBQzlELDhEQUFDbEI7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNN0MsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNaZ0IsS0FBSzJELEtBQUssQ0FBQyxHQUFHLEdBQUdySSxHQUFHLENBQUMsQ0FBQ0MsTUFBTWdJO3dCQUMzQixNQUFNMUcsYUFBYSxDQUFDLEtBQU1wQixJQUFJLEdBQUd1SCxRQUFTLEdBQUUsRUFBR3RGLE9BQU8sQ0FBQzt3QkFDdkQscUJBQ0UsOERBQUNxQjs0QkFBZ0JDLFdBQVU7OzhDQUN6Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FDQ0MsV0FBVTs0Q0FDVjdDLE9BQU87Z0RBQUV5SCxpQkFBaUJYLE1BQU0sQ0FBQ00sTUFBTTs0Q0FBQzs7Ozs7O3NEQUUxQyw4REFBQ007NENBQUs3RSxXQUFVO3NEQUE2Q3pELEtBQUtnRCxJQUFJOzs7Ozs7Ozs7Ozs7OENBRXhFLDhEQUFDUTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOztnREFBb0NuQztnREFBVzs7Ozs7OztzREFDOUQsOERBQUNrQzs0Q0FBSUMsV0FBVTtzREFBeUJqRCxlQUFlUixLQUFLRSxJQUFJOzs7Ozs7Ozs7Ozs7OzJCQVYxRDhIOzs7OztvQkFjZDs7Ozs7Ozs7Ozs7O0lBSVI7SUFFQSxxQkFDRSw4REFBQ3hFO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNJOzRCQUFHSixXQUFVO3NDQUFxQzs7Ozs7O3NDQUduRCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FBT04sV0FBVTs4Q0FBa0U7Ozs7Ozs4Q0FHcEYsOERBQUNNO29DQUFPTixXQUFVOzhDQUErRDs7Ozs7OzhDQUdqRiw4REFBQ007b0NBQU9OLFdBQVU7OENBQStEOzs7Ozs7OENBR2pGLDhEQUFDTTtvQ0FBT04sV0FBVTs4Q0FBK0Q7Ozs7Ozs4Q0FHakYsOERBQUNNO29DQUFPTixXQUFVOzhDQUErRDs7Ozs7OzhDQUdqRiw4REFBQ007b0NBQU9OLFdBQVU7OENBQStEOzs7Ozs7Ozs7Ozs7c0NBTW5GLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM4RTtnREFDQ3ZHLE1BQUs7Z0RBQ0x3RyxhQUFZO2dEQUNaL0UsV0FBVTs7Ozs7OzBEQUVaLDhEQUFDMEI7Z0RBQUkxQixXQUFVO2dEQUFnRDRDLE1BQUs7Z0RBQU9DLFFBQU87Z0RBQWVsQixTQUFROzBEQUN2Ryw0RUFBQ2dCO29EQUFLVyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUVQsYUFBYTtvREFBRzNCLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSTNFLDhEQUFDcEI7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNnRjt3Q0FDQ3ZHLE9BQU92Rjt3Q0FDUCtMLFVBQVUsQ0FBQ0MsSUFBTS9MLGFBQWErTCxFQUFFQyxNQUFNLENBQUMxRyxLQUFLO3dDQUM1Q3VCLFdBQVU7OzBEQUVWLDhEQUFDb0Y7Z0RBQU8zRyxPQUFNOzBEQUFJOzs7Ozs7MERBQ2xCLDhEQUFDMkc7Z0RBQU8zRyxPQUFNOzBEQUFLOzs7Ozs7MERBQ25CLDhEQUFDMkc7Z0RBQU8zRyxPQUFNOzBEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFPMUJOLHlCQUNDLDhEQUFDNEI7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNzRjtnREFBR3JGLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM2RTt3REFBSzdFLFdBQVU7a0VBQ2J4QyxhQUFhVyxRQUFRMEIsY0FBYzs7Ozs7O29EQUVyQ2lCLDhCQUNDLDhEQUFDZjt3REFBSUMsV0FBVywyREFFZixPQURDYyxhQUFhaEQsVUFBVSxHQUFHLG1DQUFtQzs7NERBRTVEZ0QsYUFBYWhELFVBQVUsR0FBRyxNQUFNOzREQUFJZ0QsYUFBYWpELFVBQVUsQ0FBQ2EsT0FBTyxDQUFDOzREQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtoRiw4REFBQ3FCO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDdkksME1BQVlBOzRDQUFDdUksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNOUIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEOzswREFDQyw4REFBQ3NGO2dEQUFHckYsV0FBVTswREFBeUM7Ozs7OzswREFDdkQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzZFO3dEQUFLN0UsV0FBVTtrRUFDYjdCLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU21ILGVBQWUsSUFBRyxHQUFzQyxPQUFuQ25ILFFBQVFtSCxlQUFlLENBQUM1RyxPQUFPLENBQUMsSUFBRyxRQUFNOzs7Ozs7a0VBRTFFLDhEQUFDcUI7d0RBQUlDLFdBQVU7a0VBQTJGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzlHLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3JJLDBNQUFTQTs0Q0FBQ3FJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTNCLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNzRjtnREFBR3JGLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM2RTt3REFBSzdFLFdBQVU7OzREQUNiN0IsUUFBUVEsWUFBWSxDQUFDRCxPQUFPLENBQUM7NERBQUc7Ozs7Ozs7a0VBRW5DLDhEQUFDcUI7d0RBQUlDLFdBQVcsMkRBRWYsT0FEQzdCLFFBQVFRLFlBQVksSUFBSSxLQUFLLG1DQUFtQztrRUFFL0RSLFFBQVFRLFlBQVksSUFBSSxLQUFLLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJL0MsOERBQUNvQjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ2pJLDBNQUFlQTs0Q0FBQ2lJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWpDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNzRjtnREFBR3JGLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM2RTt3REFBSzdFLFdBQVU7a0VBQ2JqRCxlQUFlb0IsUUFBUUUsVUFBVTs7Ozs7O29EQUVuQ3dDLDJCQUNDLDhEQUFDZDt3REFBSUMsV0FBVywyREFFZixPQURDYSxVQUFVL0MsVUFBVSxHQUFHLCtCQUErQjs7NERBRXJEK0MsVUFBVS9DLFVBQVUsR0FBRyxNQUFNOzREQUFJK0MsVUFBVWhELFVBQVUsQ0FBQ2EsT0FBTyxDQUFDOzREQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUsxRSw4REFBQ3FCO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDdEksME1BQWtCQTs0Q0FBQ3NJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBU3hDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUNzRjtvREFBR3JGLFdBQVU7OERBQXlCOzs7Ozs7OERBQ3ZDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM2RTs0REFBSzdFLFdBQVU7c0VBQ2JqRCxlQUFlb0IsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTRSxVQUFVLEtBQUk7Ozs7OztzRUFFekMsOERBQUMwQjs0REFBSUMsV0FBVTtzRUFBcUY7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLeEcsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDMEI7Z0RBQUkxQixXQUFVO2dEQUF3QjRDLE1BQUs7Z0RBQU9DLFFBQU87Z0RBQWVsQixTQUFROzBEQUMvRSw0RUFBQ2dCO29EQUFLVyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUVQsYUFBYTtvREFBRzNCLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzNFLDhEQUFDcEI7b0NBQUlDLFdBQVU7OENBQ1o7MkNBQUlDLE1BQU07cUNBQUksQ0FBQzNELEdBQUcsQ0FBQyxDQUFDNEQsR0FBR0Msa0JBQ3RCLDhEQUFDSjs0Q0FFQ0MsV0FBVTs0Q0FDVjdDLE9BQU87Z0RBQ0xzRixRQUFRLEdBQTJCLE9BQXhCMUUsS0FBS3dILE1BQU0sS0FBSyxLQUFLLElBQUc7Z0RBQ25DL0MsT0FBTzs0Q0FDVDsyQ0FMS3JDOzs7Ozs7Ozs7Ozs7Ozs7O3NDQVliLDhEQUFDSjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7OzhEQUNDLDhEQUFDc0Y7b0RBQUdyRixXQUFVOzhEQUF5Qjs7Ozs7OzhEQUN2Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDNkU7NERBQUs3RSxXQUFVO3NFQUNiN0IsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTbUgsZUFBZSxJQUFHLEdBQXNDLE9BQW5DbkgsUUFBUW1ILGVBQWUsQ0FBQzVHLE9BQU8sQ0FBQyxJQUFHLFFBQU07Ozs7OztzRUFFMUUsOERBQUNxQjs0REFBSUMsV0FBVTtzRUFBMkY7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLOUcsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDckksME1BQVNBO2dEQUFDcUksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSXpCLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzBCO3dDQUFJMUIsV0FBVTtrREFDYiw0RUFBQzJDOzRDQUNDeEIsR0FBRTs0Q0FDRjBCLFFBQU87NENBQ1BDLGFBQVk7NENBQ1pGLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT2IsOERBQUM3Qzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7OzhEQUNDLDhEQUFDc0Y7b0RBQUdyRixXQUFVOzhEQUF5Qjs7Ozs7OzhEQUN2Qyw4REFBQ0s7b0RBQUVMLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3JDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM2RTs0REFBSzdFLFdBQVU7c0VBQ2I3QixVQUFVLEdBQXFGLE9BQWxGLENBQUMsQ0FBQ0EsUUFBUXdCLGtCQUFrQixHQUFHeEIsUUFBUXlCLG1CQUFtQixJQUFJLE9BQU0sRUFBR2xCLE9BQU8sQ0FBQyxJQUFHLE9BQUs7Ozs7OztzRUFFdkcsOERBQUNxQjs0REFBSUMsV0FBVTtzRUFBeUY7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLNUcsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDcEksME1BQVdBO2dEQUFDb0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSTNCLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzBCO3dDQUFJMUIsV0FBVTtrREFDWjsrQ0FBSUMsTUFBTTt5Q0FBSSxDQUFDM0QsR0FBRyxDQUFDLENBQUM0RCxHQUFHQyxrQkFDdEIsOERBQUN3RDtnREFFQ0MsSUFBSTdGLEtBQUt3SCxNQUFNLEtBQUssTUFBTTtnREFDMUIxQixJQUFJOUYsS0FBS3dILE1BQU0sS0FBSyxNQUFNO2dEQUMxQnpCLEdBQUU7Z0RBQ0ZsQixNQUFNO29EQUFDO29EQUFXO29EQUFXO2lEQUFVLENBQUM3RSxLQUFLeUgsS0FBSyxDQUFDekgsS0FBS3dILE1BQU0sS0FBSyxHQUFHOytDQUpqRXBGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBWWYsOERBQUNKOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUNzRjtvREFBR3JGLFdBQVU7OERBQXlCOzs7Ozs7OERBQ3ZDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM2RTs0REFBSzdFLFdBQVU7c0VBQ2J4QyxhQUFhVyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMwQixjQUFjLEtBQUk7Ozs7OztzRUFFM0MsOERBQUNFOzREQUFJQyxXQUFVO3NFQUF5Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUs1Ryw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUN2SSwwTUFBWUE7Z0RBQUN1SSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJNUIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaOzJDQUFJQyxNQUFNO3FDQUFHLENBQUMzRCxHQUFHLENBQUMsQ0FBQzRELEdBQUdDLGtCQUNyQiw4REFBQ0o7NENBQVlDLFdBQVU7c0RBQ3JCLDRFQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQ0NDLFdBQVU7b0RBQ1Y3QyxPQUFPO3dEQUFFcUYsT0FBTyxHQUEyQixPQUF4QnpFLEtBQUt3SCxNQUFNLEtBQUssS0FBSyxJQUFHO29EQUFHOzs7Ozs7Ozs7OzsyQ0FKMUNwRjs7Ozs7Ozs7Ozs7Ozs7OztzQ0FhaEIsOERBQUNKOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUNzRjtvREFBR3JGLFdBQVU7OERBQXlCOzs7Ozs7OERBQ3ZDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM2RTs0REFBSzdFLFdBQVU7c0VBQXFDOzs7Ozs7c0VBQ3JELDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBeUY7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLNUcsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDMEI7Z0RBQUkxQixXQUFVO2dEQUF3QjRDLE1BQUs7Z0RBQU9DLFFBQU87Z0RBQWVsQixTQUFROzBEQUMvRSw0RUFBQ2dCO29EQUFLVyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUVQsYUFBYTtvREFBRzNCLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzNFLDhEQUFDcEI7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUMwQjt3Q0FBSTFCLFdBQVU7a0RBQ2IsNEVBQUMyQzs0Q0FDQ3hCLEdBQUU7NENBQ0YwQixRQUFPOzRDQUNQQyxhQUFZOzRDQUNaRixNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVFkekUseUJBQ0MsOERBQUM0QjtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEOzswREFDQyw4REFBQ3NGO2dEQUFHckYsV0FBVTswREFBeUM7Ozs7OzswREFDdkQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzZFO3dEQUFLN0UsV0FBVTtrRUFDYnhDLGFBQWFXLFFBQVEwQixjQUFjOzs7Ozs7b0RBRXJDaUIsOEJBQ0MsOERBQUNmO3dEQUFJQyxXQUFXLDJEQUVmLE9BRENjLGFBQWFoRCxVQUFVLEdBQUcsbUNBQW1DOzs0REFFNURnRCxhQUFhaEQsVUFBVSxHQUFHLE1BQU07NERBQUlnRCxhQUFhakQsVUFBVSxDQUFDYSxPQUFPLENBQUM7NERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS2hGLDhEQUFDcUI7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNoSSwwTUFBYUE7NENBQUNnSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU0vQiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDc0Y7Z0RBQUdyRixXQUFVOzBEQUF5Qzs7Ozs7OzBEQUN2RCw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDNkU7d0RBQUs3RSxXQUFVO2tFQUNiN0IsUUFBUW1ILGVBQWUsR0FBRyxHQUFzQyxPQUFuQ25ILFFBQVFtSCxlQUFlLENBQUM1RyxPQUFPLENBQUMsSUFBRyxRQUFNOzs7Ozs7a0VBRXpFLDhEQUFDcUI7d0RBQUlDLFdBQVU7a0VBQTJGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzlHLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3JJLDBNQUFTQTs0Q0FBQ3FJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTNCLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNzRjtnREFBR3JGLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM2RTt3REFBSzdFLFdBQVU7OzREQUNiN0IsUUFBUVEsWUFBWSxDQUFDRCxPQUFPLENBQUM7NERBQUc7Ozs7Ozs7a0VBRW5DLDhEQUFDcUI7d0RBQUlDLFdBQVcsMkRBRWYsT0FEQzdCLFFBQVFRLFlBQVksSUFBSSxLQUFLLG1DQUFtQztrRUFFL0RSLFFBQVFRLFlBQVksSUFBSSxLQUFLLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJL0MsOERBQUNvQjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ2pJLDBNQUFlQTs0Q0FBQ2lJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWpDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNzRjtnREFBR3JGLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM2RTt3REFBSzdFLFdBQVU7a0VBQ2JqRCxlQUFlb0IsUUFBUUUsVUFBVTs7Ozs7O29EQUVuQ3dDLDJCQUNDLDhEQUFDZDt3REFBSUMsV0FBVywyREFFZixPQURDYSxVQUFVL0MsVUFBVSxHQUFHLCtCQUErQjs7NERBRXJEK0MsVUFBVS9DLFVBQVUsR0FBRyxNQUFNOzREQUFJK0MsVUFBVWhELFVBQVUsQ0FBQ2EsT0FBTyxDQUFDOzREQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUsxRSw4REFBQ3FCO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDdEksME1BQWtCQTs0Q0FBQ3NJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUXhDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDcUY7NENBQUdyRixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDNkU7b0RBQUs3RSxXQUFVOzhEQUNiakQsZUFBZW9CLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU0UsVUFBVSxLQUFJOzs7Ozs7OERBRXpDLDhEQUFDMEI7b0RBQUlDLFdBQVU7OERBQXFGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTXhHLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzZFO29EQUFLN0UsV0FBVTs4REFBZ0I7Ozs7Ozs4REFDaEMsOERBQUM2RTtvREFBSzdFLFdBQVU7OERBQWNqRCxlQUFlb0IsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTRSxVQUFVLEtBQUk7Ozs7Ozs7Ozs7OztzREFFdEUsOERBQUMwQjs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7Z0RBQWdDN0MsT0FBTztvREFBRXFGLE9BQU87Z0RBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU0zRSw4REFBQ3pDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDcUY7NENBQUdyRixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDNkU7b0RBQUs3RSxXQUFVOzhEQUNiN0IsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTbUgsZUFBZSxJQUFHLEdBQXNDLE9BQW5DbkgsUUFBUW1ILGVBQWUsQ0FBQzVHLE9BQU8sQ0FBQyxJQUFHLFFBQU07Ozs7Ozs4REFFMUUsOERBQUNxQjtvREFBSUMsV0FBVTs4REFBMkY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNOUcsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDNkU7b0RBQUs3RSxXQUFVOzhEQUFnQjs7Ozs7OzhEQUNoQyw4REFBQzZFO29EQUFLN0UsV0FBVTs4REFBYzdCLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU21ILGVBQWUsSUFBRyxHQUFzQyxPQUFuQ25ILFFBQVFtSCxlQUFlLENBQUM1RyxPQUFPLENBQUMsSUFBRyxRQUFNOzs7Ozs7Ozs7Ozs7c0RBRXZHLDhEQUFDcUI7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVO2dEQUFpQzdDLE9BQU87b0RBQUVxRixPQUFPO2dEQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNNUUsOERBQUN6Qzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3FGOzRDQUFHckYsV0FBVTtzREFBeUM7Ozs7OztzREFDdkQsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzZFO29EQUFLN0UsV0FBVTs4REFDYjdCLFVBQVUsR0FBcUYsT0FBbEYsQ0FBQyxDQUFDQSxRQUFRd0Isa0JBQWtCLEdBQUd4QixRQUFReUIsbUJBQW1CLElBQUksT0FBTSxFQUFHbEIsT0FBTyxDQUFDLElBQUcsT0FBSzs7Ozs7OzhEQUV2Ryw4REFBQ3FCO29EQUFJQyxXQUFVOzhEQUF5Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU01Ryw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUM2RTtvREFBSzdFLFdBQVU7OERBQWdCOzs7Ozs7OERBQ2hDLDhEQUFDNkU7b0RBQUs3RSxXQUFVOzhEQUFjN0IsVUFBVVgsYUFBYVcsUUFBUXdCLGtCQUFrQixHQUFHeEIsUUFBUXlCLG1CQUFtQixJQUFJOzs7Ozs7Ozs7Ozs7c0RBRW5ILDhEQUFDRzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7Z0RBQWlDN0MsT0FBTztvREFBRXFGLE9BQU87Z0RBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU85RSw4REFBQ3pDO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNxRjs0Q0FBR3JGLFdBQVU7c0RBQXdDOzs7Ozs7c0RBQ3RELDhEQUFDSzs0Q0FBRUwsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7OztnQ0FFdEM5SCxDQUFBQSwwQkFBQUEsb0NBQUFBLGNBQWVtRSxZQUFZLENBQUM0RSxNQUFNLGtCQUNqQyw4REFBQ2xCO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDeUY7d0NBQU16RixXQUFVOzswREFDZiw4REFBQzBGOzBEQUNDLDRFQUFDQztvREFBRzNGLFdBQVU7O3NFQUNaLDhEQUFDNEY7NERBQUc1RixXQUFVO3NFQUFtRDs7Ozs7O3NFQUNqRSw4REFBQzRGOzREQUFHNUYsV0FBVTtzRUFBb0Q7Ozs7OztzRUFDbEUsOERBQUM0Rjs0REFBRzVGLFdBQVU7c0VBQW9EOzs7Ozs7c0VBQ2xFLDhEQUFDNEY7NERBQUc1RixXQUFVO3NFQUFvRDs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR3RFLDhEQUFDNkY7Z0RBQU03RixXQUFVOzBEQUNkOUgsY0FBY21FLFlBQVksQ0FDeEI2QyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRTNDLElBQUksR0FBRzBDLEVBQUUxQyxJQUFJLEVBQzlCSCxHQUFHLENBQUMsQ0FBQ3dKLFVBQVV2QjtvREFDZCxNQUFNTixTQUFTO3dEQUFDO3dEQUFXO3dEQUFXO3dEQUFXO3dEQUFXO3dEQUFXO3FEQUFVO29EQUNqRixNQUFNcEcsYUFBYSxDQUFDLFNBQVVwQixJQUFJLEdBQUkwQixDQUFBQSxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNFLFVBQVUsS0FBSSxLQUFNLEdBQUUsRUFBR0ssT0FBTyxDQUFDO29EQUNoRixxQkFDRSw4REFBQ2lIO3dEQUF1QjNGLFdBQVU7OzBFQUNoQyw4REFBQytGO2dFQUFHL0YsV0FBVTswRUFDWiw0RUFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFDQ0MsV0FBVTs0RUFDVjdDLE9BQU87Z0ZBQUV5SCxpQkFBaUJYLE1BQU0sQ0FBQ00sUUFBUU4sT0FBT2hELE1BQU0sQ0FBQzs0RUFBQzs7Ozs7O3NGQUUxRCw4REFBQzREOzRFQUFLN0UsV0FBVTtzRkFBcUM4RixTQUFTdkcsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBR3RFLDhEQUFDd0c7Z0VBQUcvRixXQUFVOzBFQUNYakQsZUFBZStJLFNBQVNySixJQUFJOzs7Ozs7MEVBRS9CLDhEQUFDc0o7Z0VBQUcvRixXQUFVOzBFQUNYeEMsYUFBYXNJLFNBQVNwSixRQUFROzs7Ozs7MEVBRWpDLDhEQUFDcUo7Z0VBQUcvRixXQUFVOzBFQUNaLDRFQUFDNkU7b0VBQUs3RSxXQUFVOzt3RUFDYm5DO3dFQUFXOzs7Ozs7Ozs7Ozs7O3VEQWxCVGlJLFNBQVN2RyxJQUFJOzs7OztnREF1QjFCOzs7Ozs7Ozs7Ozs7Ozs7O3lEQUtSLDhEQUFDUTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNsSSwwTUFBWUE7NENBQUNrSSxXQUFVOzs7Ozs7c0RBQ3hCLDhEQUFDSztzREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1ULDhEQUFDTjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3FGOzRDQUFHckYsV0FBVTtzREFBd0M7Ozs7OztzREFDdEQsOERBQUNLOzRDQUFFTCxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7O2dDQUd0QzVILENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JpRSxZQUFZLENBQUM0RSxNQUFNLGtCQUNsQyw4REFBQ2xCO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDeUY7d0NBQU16RixXQUFVOzswREFDZiw4REFBQzBGOzBEQUNDLDRFQUFDQztvREFBRzNGLFdBQVU7O3NFQUNaLDhEQUFDNEY7NERBQUc1RixXQUFVO3NFQUFtRDs7Ozs7O3NFQUNqRSw4REFBQzRGOzREQUFHNUYsV0FBVTtzRUFBb0Q7Ozs7OztzRUFDbEUsOERBQUM0Rjs0REFBRzVGLFdBQVU7c0VBQW9EOzs7Ozs7c0VBQ2xFLDhEQUFDNEY7NERBQUc1RixXQUFVO3NFQUFvRDs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR3RFLDhEQUFDNkY7Z0RBQU03RixXQUFVOzBEQUNkNUgsZUFBZWlFLFlBQVksQ0FDekI2QyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRTNDLElBQUksR0FBRzBDLEVBQUUxQyxJQUFJLEVBQzlCa0ksS0FBSyxDQUFDLEdBQUcsR0FDVHJJLEdBQUcsQ0FBQyxDQUFDMEosT0FBT3pCO29EQUNYLE1BQU1OLFNBQVM7d0RBQUM7d0RBQVc7d0RBQVc7d0RBQVc7d0RBQVc7cURBQVU7b0RBQ3RFLE1BQU1nQyxVQUFVRCxNQUFNdkosSUFBSSxHQUFHc0IsS0FBSzBDLEdBQUcsQ0FBQ3VGLE1BQU10SixRQUFRLEVBQUU7b0RBQ3RELHFCQUNFLDhEQUFDaUo7d0RBQW9CM0YsV0FBVTs7MEVBQzdCLDhEQUFDK0Y7Z0VBQUcvRixXQUFVOzBFQUNaLDRFQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUNDQyxXQUFVOzRFQUNWN0MsT0FBTztnRkFBRXlILGlCQUFpQlgsTUFBTSxDQUFDTSxRQUFRTixPQUFPaEQsTUFBTSxDQUFDOzRFQUFDOzs7Ozs7c0ZBRTFELDhEQUFDbEI7OzhGQUNDLDhEQUFDOEU7b0ZBQUs3RSxXQUFVOzhGQUEwQmdHLE1BQU16RyxJQUFJLENBQUMwQixNQUFNLEdBQUcsS0FBSytFLE1BQU16RyxJQUFJLENBQUMyRyxTQUFTLENBQUMsR0FBRyxNQUFNLFFBQVFGLE1BQU16RyxJQUFJOzs7Ozs7OEZBQ25ILDhEQUFDUTtvRkFBSUMsV0FBVTs7d0ZBQ1p4QyxhQUFhd0ksTUFBTXBKLFlBQVksR0FBR29KLE1BQU1uSixhQUFhO3dGQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBS2hFLDhEQUFDa0o7Z0VBQUcvRixXQUFVOzBFQUNYakQsZUFBZWlKLE1BQU12SixJQUFJOzs7Ozs7MEVBRTVCLDhEQUFDc0o7Z0VBQUcvRixXQUFVOzBFQUNYeEMsYUFBYXdJLE1BQU10SixRQUFROzs7Ozs7MEVBRTlCLDhEQUFDcUo7Z0VBQUcvRixXQUFVOzBFQUNYakQsZUFBZWtKOzs7Ozs7O3VEQXRCWEQsTUFBTXpHLElBQUk7Ozs7O2dEQTBCdkI7Ozs7Ozs7Ozs7Ozs7Ozs7eURBS1IsOERBQUNRO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3BJLDBNQUFXQTs0Q0FBQ29JLFdBQVU7Ozs7OztzREFDdkIsOERBQUNLO3NEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWdCbkI7R0FqbkNTcEk7S0FBQUE7QUFtbkNNLFNBQVNrTztJQUN0QixxQkFDRSw4REFBQzNPLDJDQUFRQTtRQUFDNE8sd0JBQ1IsOERBQUNyRztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzhCQUVqQiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1o7MkJBQUlDLE1BQU07cUJBQUcsQ0FBQzNELEdBQUcsQ0FBQyxDQUFDNEQsR0FBR0Msa0JBQ3JCLDhEQUFDSjs0QkFBWUMsV0FBVTs7OENBQ3JCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzsyQkFIUEc7Ozs7Ozs7Ozs7Ozs7Ozs7a0JBU2hCLDRFQUFDbEk7Ozs7Ozs7Ozs7QUFHUDtNQXRCd0JrTyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxcYW5hbHl0aWNzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCBTdXNwZW5zZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIENoYXJ0QmFySWNvbixcbiAgQ3VycmVuY3lEb2xsYXJJY29uLFxuICBDbG9ja0ljb24sXG4gIENwdUNoaXBJY29uLFxuICBDYWxlbmRhckljb24sXG4gIEZ1bm5lbEljb24sXG4gIEFycm93VHJlbmRpbmdVcEljb24sXG4gIEFycm93VHJlbmRpbmdEb3duSWNvbixcbiAgQ2hlY2tDaXJjbGVJY29uLFxuICBFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbixcbiAgU3BhcmtsZXNJY29uLFxuICBCb2x0SWNvbixcbiAgTGlnaHRCdWxiSWNvbixcbiAgQmVsbEljb24sXG4gIENoYXJ0UGllSWNvbixcbiAgSGFuZFRodW1iVXBJY29uLFxuICBBcnJvd1BhdGhJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbmludGVyZmFjZSBBbmFseXRpY3NEYXRhIHtcbiAgc3VtbWFyeToge1xuICAgIHRvdGFsX3JlcXVlc3RzOiBudW1iZXI7XG4gICAgc3VjY2Vzc2Z1bF9yZXF1ZXN0czogbnVtYmVyO1xuICAgIHN1Y2Nlc3NfcmF0ZTogbnVtYmVyO1xuICAgIHRvdGFsX2Nvc3Q6IG51bWJlcjtcbiAgICB0b3RhbF9pbnB1dF90b2tlbnM6IG51bWJlcjtcbiAgICB0b3RhbF9vdXRwdXRfdG9rZW5zOiBudW1iZXI7XG4gICAgYXZlcmFnZV9jb3N0X3Blcl9yZXF1ZXN0OiBudW1iZXI7XG4gIH07XG4gIGdyb3VwZWRfZGF0YTogQXJyYXk8e1xuICAgIG5hbWU6IHN0cmluZztcbiAgICByZXF1ZXN0czogbnVtYmVyO1xuICAgIGNvc3Q6IG51bWJlcjtcbiAgICBpbnB1dF90b2tlbnM6IG51bWJlcjtcbiAgICBvdXRwdXRfdG9rZW5zOiBudW1iZXI7XG4gICAgc3VjY2Vzc19yYXRlPzogbnVtYmVyO1xuICAgIHBlcmlvZD86IHN0cmluZzsgLy8gRm9yIHRpbWUgc2VyaWVzIGRhdGFcbiAgfT47XG59XG5cbmludGVyZmFjZSBDdXN0b21BcGlDb25maWcge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBUaW1lU2VyaWVzRGF0YSB7XG4gIHBlcmlvZDogc3RyaW5nO1xuICBjb3N0OiBudW1iZXI7XG4gIHJlcXVlc3RzOiBudW1iZXI7XG4gIHRva2VuczogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgTGF0ZW5jeURhdGEge1xuICBwcm92aWRlcjogc3RyaW5nO1xuICBhdmdfbGF0ZW5jeTogbnVtYmVyO1xuICBwOTVfbGF0ZW5jeTogbnVtYmVyO1xuICByZXF1ZXN0czogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgQ29zdEFsZXJ0IHtcbiAgdHlwZTogJ3dhcm5pbmcnIHwgJ2RhbmdlcicgfCAnaW5mbyc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgdmFsdWU/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBVc2FnZVJlY29tbWVuZGF0aW9uIHtcbiAgdHlwZTogJ2Nvc3Rfb3B0aW1pemF0aW9uJyB8ICdwZXJmb3JtYW5jZScgfCAnZWZmaWNpZW5jeSc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIHBvdGVudGlhbF9zYXZpbmdzPzogbnVtYmVyO1xuICBpY29uOiBhbnk7XG59XG5cbmZ1bmN0aW9uIEFuYWx5dGljc1BhZ2VDb250ZW50KCkge1xuICBjb25zdCBbYW5hbHl0aWNzRGF0YSwgc2V0QW5hbHl0aWNzRGF0YV0gPSB1c2VTdGF0ZTxBbmFseXRpY3NEYXRhIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFttb2RlbEFuYWx5dGljcywgc2V0TW9kZWxBbmFseXRpY3NdID0gdXNlU3RhdGU8QW5hbHl0aWNzRGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY29uZmlnQW5hbHl0aWNzLCBzZXRDb25maWdBbmFseXRpY3NdID0gdXNlU3RhdGU8QW5hbHl0aWNzRGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbdGltZVNlcmllc0RhdGEsIHNldFRpbWVTZXJpZXNEYXRhXSA9IHVzZVN0YXRlPFRpbWVTZXJpZXNEYXRhW10+KFtdKTtcbiAgY29uc3QgW3ByZXZpb3VzUGVyaW9kRGF0YSwgc2V0UHJldmlvdXNQZXJpb2REYXRhXSA9IHVzZVN0YXRlPEFuYWx5dGljc0RhdGEgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2N1c3RvbUNvbmZpZ3MsIHNldEN1c3RvbUNvbmZpZ3NdID0gdXNlU3RhdGU8Q3VzdG9tQXBpQ29uZmlnW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gRmlsdGVyc1xuICBjb25zdCBbdGltZVJhbmdlLCBzZXRUaW1lUmFuZ2VdID0gdXNlU3RhdGUoJzMwJyk7XG4gIGNvbnN0IFtzZWxlY3RlZENvbmZpZywgc2V0U2VsZWN0ZWRDb25maWddID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtzaG93RmlsdGVycywgc2V0U2hvd0ZpbHRlcnNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc3RhcnREYXRlLCBzZXRTdGFydERhdGVdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbZW5kRGF0ZSwgc2V0RW5kRGF0ZV0gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgLy8gQnVkZ2V0IHNldHRpbmdzXG4gIGNvbnN0IFttb250aGx5QnVkZ2V0LCBzZXRNb250aGx5QnVkZ2V0XSA9IHVzZVN0YXRlPG51bWJlcj4oMTAwKTsgLy8gRGVmYXVsdCAkMTAwL21vbnRoXG4gIGNvbnN0IFtzaG93QnVkZ2V0U2V0dGluZ3MsIHNldFNob3dCdWRnZXRTZXR0aW5nc10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEN1c3RvbUNvbmZpZ3MoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGZldGNoQ3VzdG9tQ29uZmlncyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jdXN0b20tY29uZmlncycpO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGNvbmZpZ3MgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldEN1c3RvbUNvbmZpZ3MoY29uZmlncyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjb25maWdzOicsIGVycik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoQW5hbHl0aWNzRGF0YSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAvLyBCdWlsZCBxdWVyeSBwYXJhbWV0ZXJzIGZvciBjdXJyZW50IHBlcmlvZFxuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgICAgbGV0IGN1cnJlbnRTdGFydERhdGU6IERhdGU7XG4gICAgICBsZXQgY3VycmVudEVuZERhdGU6IERhdGUgPSBuZXcgRGF0ZSgpO1xuXG4gICAgICBpZiAoc3RhcnREYXRlICYmIGVuZERhdGUpIHtcbiAgICAgICAgY3VycmVudFN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHN0YXJ0RGF0ZSk7XG4gICAgICAgIGN1cnJlbnRFbmREYXRlID0gbmV3IERhdGUoZW5kRGF0ZSk7XG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ3N0YXJ0RGF0ZScsIGN1cnJlbnRTdGFydERhdGUudG9JU09TdHJpbmcoKSk7XG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ2VuZERhdGUnLCBjdXJyZW50RW5kRGF0ZS50b0lTT1N0cmluZygpKTtcbiAgICAgIH0gZWxzZSBpZiAodGltZVJhbmdlKSB7XG4gICAgICAgIGN1cnJlbnRTdGFydERhdGUgPSBuZXcgRGF0ZSgpO1xuICAgICAgICBjdXJyZW50U3RhcnREYXRlLnNldERhdGUoY3VycmVudFN0YXJ0RGF0ZS5nZXREYXRlKCkgLSBwYXJzZUludCh0aW1lUmFuZ2UpKTtcbiAgICAgICAgcGFyYW1zLmFwcGVuZCgnc3RhcnREYXRlJywgY3VycmVudFN0YXJ0RGF0ZS50b0lTT1N0cmluZygpKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHNlbGVjdGVkQ29uZmlnKSB7XG4gICAgICAgIHBhcmFtcy5hcHBlbmQoJ2N1c3RvbUFwaUNvbmZpZ0lkJywgc2VsZWN0ZWRDb25maWcpO1xuICAgICAgfVxuXG4gICAgICAvLyBCdWlsZCBxdWVyeSBwYXJhbWV0ZXJzIGZvciBwcmV2aW91cyBwZXJpb2QgKGZvciBjb21wYXJpc29uKVxuICAgICAgY29uc3QgcHJldlBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICAgIGNvbnN0IHBlcmlvZExlbmd0aCA9IGN1cnJlbnRFbmREYXRlLmdldFRpbWUoKSAtIGN1cnJlbnRTdGFydERhdGUhLmdldFRpbWUoKTtcbiAgICAgIGNvbnN0IHByZXZTdGFydERhdGUgPSBuZXcgRGF0ZShjdXJyZW50U3RhcnREYXRlIS5nZXRUaW1lKCkgLSBwZXJpb2RMZW5ndGgpO1xuICAgICAgY29uc3QgcHJldkVuZERhdGUgPSBuZXcgRGF0ZShjdXJyZW50U3RhcnREYXRlIS5nZXRUaW1lKCkpO1xuXG4gICAgICBwcmV2UGFyYW1zLmFwcGVuZCgnc3RhcnREYXRlJywgcHJldlN0YXJ0RGF0ZS50b0lTT1N0cmluZygpKTtcbiAgICAgIHByZXZQYXJhbXMuYXBwZW5kKCdlbmREYXRlJywgcHJldkVuZERhdGUudG9JU09TdHJpbmcoKSk7XG4gICAgICBpZiAoc2VsZWN0ZWRDb25maWcpIHtcbiAgICAgICAgcHJldlBhcmFtcy5hcHBlbmQoJ2N1c3RvbUFwaUNvbmZpZ0lkJywgc2VsZWN0ZWRDb25maWcpO1xuICAgICAgfVxuXG4gICAgICAvLyBGZXRjaCBtdWx0aXBsZSBhbmFseXRpY3Mgdmlld3NcbiAgICAgIGNvbnN0IFtcbiAgICAgICAgcHJvdmlkZXJSZXNwb25zZSxcbiAgICAgICAgbW9kZWxSZXNwb25zZSxcbiAgICAgICAgdGltZVNlcmllc1Jlc3BvbnNlLFxuICAgICAgICBwcmV2aW91c1BlcmlvZFJlc3BvbnNlXG4gICAgICBdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBmZXRjaChgL2FwaS9hbmFseXRpY3Mvc3VtbWFyeT8ke3BhcmFtcy50b1N0cmluZygpfSZncm91cEJ5PXByb3ZpZGVyYCksXG4gICAgICAgIGZldGNoKGAvYXBpL2FuYWx5dGljcy9zdW1tYXJ5PyR7cGFyYW1zLnRvU3RyaW5nKCl9Jmdyb3VwQnk9bW9kZWxgKSxcbiAgICAgICAgZmV0Y2goYC9hcGkvYW5hbHl0aWNzL3N1bW1hcnk/JHtwYXJhbXMudG9TdHJpbmcoKX0mZ3JvdXBCeT1kYXlgKSxcbiAgICAgICAgZmV0Y2goYC9hcGkvYW5hbHl0aWNzL3N1bW1hcnk/JHtwcmV2UGFyYW1zLnRvU3RyaW5nKCl9Jmdyb3VwQnk9ZGF5YClcbiAgICAgIF0pO1xuXG4gICAgICBpZiAoIXByb3ZpZGVyUmVzcG9uc2Uub2sgfHwgIW1vZGVsUmVzcG9uc2Uub2sgfHwgIXRpbWVTZXJpZXNSZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBhbmFseXRpY3MgZGF0YScpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBwcm92aWRlckRhdGEgPSBhd2FpdCBwcm92aWRlclJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnN0IG1vZGVsRGF0YSA9IGF3YWl0IG1vZGVsUmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc3QgdGltZVNlcmllc1Jlc3VsdCA9IGF3YWl0IHRpbWVTZXJpZXNSZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zdCBwcmV2aW91c1BlcmlvZFJlc3VsdCA9IHByZXZpb3VzUGVyaW9kUmVzcG9uc2Uub2sgPyBhd2FpdCBwcmV2aW91c1BlcmlvZFJlc3BvbnNlLmpzb24oKSA6IG51bGw7XG5cbiAgICAgIHNldEFuYWx5dGljc0RhdGEocHJvdmlkZXJEYXRhKTtcbiAgICAgIHNldE1vZGVsQW5hbHl0aWNzKG1vZGVsRGF0YSk7XG4gICAgICBzZXRQcmV2aW91c1BlcmlvZERhdGEocHJldmlvdXNQZXJpb2RSZXN1bHQpO1xuXG4gICAgICAvLyBQcm9jZXNzIHRpbWUgc2VyaWVzIGRhdGFcbiAgICAgIGNvbnN0IHByb2Nlc3NlZFRpbWVTZXJpZXM6IFRpbWVTZXJpZXNEYXRhW10gPSB0aW1lU2VyaWVzUmVzdWx0Lmdyb3VwZWRfZGF0YS5tYXAoKGl0ZW06IGFueSkgPT4gKHtcbiAgICAgICAgcGVyaW9kOiBpdGVtLnBlcmlvZCxcbiAgICAgICAgY29zdDogaXRlbS5jb3N0LFxuICAgICAgICByZXF1ZXN0czogaXRlbS5yZXF1ZXN0cyxcbiAgICAgICAgdG9rZW5zOiBpdGVtLmlucHV0X3Rva2VucyArIGl0ZW0ub3V0cHV0X3Rva2Vuc1xuICAgICAgfSkpO1xuICAgICAgc2V0VGltZVNlcmllc0RhdGEocHJvY2Vzc2VkVGltZVNlcmllcyk7XG5cbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgc2V0RXJyb3IoZXJyLm1lc3NhZ2UpO1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYW5hbHl0aWNzOicsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW3RpbWVSYW5nZSwgc2VsZWN0ZWRDb25maWcsIHN0YXJ0RGF0ZSwgZW5kRGF0ZV0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hBbmFseXRpY3NEYXRhKCk7XG4gIH0sIFtmZXRjaEFuYWx5dGljc0RhdGEsIHRpbWVSYW5nZSwgc2VsZWN0ZWRDb25maWcsIHN0YXJ0RGF0ZSwgZW5kRGF0ZV0pO1xuXG4gIGNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tVVMnLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5OiAnVVNEJyxcbiAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMixcbiAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogNixcbiAgICB9KS5mb3JtYXQoYW1vdW50KTtcbiAgfTtcblxuICBjb25zdCBmb3JtYXROdW1iZXIgPSAobnVtOiBudW1iZXIpID0+IHtcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdlbi1VUycpLmZvcm1hdChudW0pO1xuICB9O1xuXG4gIGNvbnN0IGNhbGN1bGF0ZVRyZW5kID0gKGN1cnJlbnQ6IG51bWJlciwgcHJldmlvdXM6IG51bWJlcikgPT4ge1xuICAgIGlmIChwcmV2aW91cyA9PT0gMCkgcmV0dXJuIHsgcGVyY2VudGFnZTogMCwgaXNQb3NpdGl2ZTogdHJ1ZSB9O1xuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSAoKGN1cnJlbnQgLSBwcmV2aW91cykgLyBwcmV2aW91cykgKiAxMDA7XG4gICAgcmV0dXJuIHsgcGVyY2VudGFnZTogTWF0aC5hYnMocGVyY2VudGFnZSksIGlzUG9zaXRpdmU6IHBlcmNlbnRhZ2UgPj0gMCB9O1xuICB9O1xuXG4gIGNvbnN0IGdlbmVyYXRlQ29zdEFsZXJ0cyA9ICgpOiBDb3N0QWxlcnRbXSA9PiB7XG4gICAgY29uc3QgYWxlcnRzOiBDb3N0QWxlcnRbXSA9IFtdO1xuICAgIGNvbnN0IHN1bW1hcnkgPSBhbmFseXRpY3NEYXRhPy5zdW1tYXJ5O1xuXG4gICAgaWYgKCFzdW1tYXJ5KSByZXR1cm4gYWxlcnRzO1xuXG4gICAgLy8gQnVkZ2V0IGFsZXJ0XG4gICAgY29uc3QgcHJvamVjdGVkTW9udGhseUNvc3QgPSAoc3VtbWFyeS50b3RhbF9jb3N0IC8gcGFyc2VJbnQodGltZVJhbmdlKSkgKiAzMDtcbiAgICBpZiAocHJvamVjdGVkTW9udGhseUNvc3QgPiBtb250aGx5QnVkZ2V0ICogMC44KSB7XG4gICAgICBhbGVydHMucHVzaCh7XG4gICAgICAgIHR5cGU6IHByb2plY3RlZE1vbnRobHlDb3N0ID4gbW9udGhseUJ1ZGdldCA/ICdkYW5nZXInIDogJ3dhcm5pbmcnLFxuICAgICAgICB0aXRsZTogcHJvamVjdGVkTW9udGhseUNvc3QgPiBtb250aGx5QnVkZ2V0ID8gJ0J1ZGdldCBFeGNlZWRlZCcgOiAnQnVkZ2V0IFdhcm5pbmcnLFxuICAgICAgICBtZXNzYWdlOiBgUHJvamVjdGVkIG1vbnRobHkgY29zdDogJHtmb3JtYXRDdXJyZW5jeShwcm9qZWN0ZWRNb250aGx5Q29zdCl9IChCdWRnZXQ6ICR7Zm9ybWF0Q3VycmVuY3kobW9udGhseUJ1ZGdldCl9KWAsXG4gICAgICAgIHZhbHVlOiBgJHsoKHByb2plY3RlZE1vbnRobHlDb3N0IC8gbW9udGhseUJ1ZGdldCkgKiAxMDApLnRvRml4ZWQoMCl9JWBcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIFN1Y2Nlc3MgcmF0ZSBhbGVydFxuICAgIGlmIChzdW1tYXJ5LnN1Y2Nlc3NfcmF0ZSA8IDk1KSB7XG4gICAgICBhbGVydHMucHVzaCh7XG4gICAgICAgIHR5cGU6IHN1bW1hcnkuc3VjY2Vzc19yYXRlIDwgOTAgPyAnZGFuZ2VyJyA6ICd3YXJuaW5nJyxcbiAgICAgICAgdGl0bGU6ICdMb3cgU3VjY2VzcyBSYXRlJyxcbiAgICAgICAgbWVzc2FnZTogYEN1cnJlbnQgc3VjY2VzcyByYXRlIGlzICR7c3VtbWFyeS5zdWNjZXNzX3JhdGUudG9GaXhlZCgxKX0lLiBDb25zaWRlciByZXZpZXdpbmcgZmFpbGVkIHJlcXVlc3RzLmAsXG4gICAgICAgIHZhbHVlOiBgJHtzdW1tYXJ5LnN1Y2Nlc3NfcmF0ZS50b0ZpeGVkKDEpfSVgXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBIaWdoIGNvc3QgcGVyIHJlcXVlc3QgYWxlcnRcbiAgICBpZiAoc3VtbWFyeS5hdmVyYWdlX2Nvc3RfcGVyX3JlcXVlc3QgPiAwLjAxKSB7XG4gICAgICBhbGVydHMucHVzaCh7XG4gICAgICAgIHR5cGU6ICd3YXJuaW5nJyxcbiAgICAgICAgdGl0bGU6ICdIaWdoIENvc3QgUGVyIFJlcXVlc3QnLFxuICAgICAgICBtZXNzYWdlOiBgQXZlcmFnZSBjb3N0IHBlciByZXF1ZXN0IGlzICR7Zm9ybWF0Q3VycmVuY3koc3VtbWFyeS5hdmVyYWdlX2Nvc3RfcGVyX3JlcXVlc3QpfS4gQ29uc2lkZXIgb3B0aW1pemluZyBtb2RlbCB1c2FnZS5gLFxuICAgICAgICB2YWx1ZTogZm9ybWF0Q3VycmVuY3koc3VtbWFyeS5hdmVyYWdlX2Nvc3RfcGVyX3JlcXVlc3QpXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gYWxlcnRzO1xuICB9O1xuXG4gIGNvbnN0IGdlbmVyYXRlUmVjb21tZW5kYXRpb25zID0gKCk6IFVzYWdlUmVjb21tZW5kYXRpb25bXSA9PiB7XG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zOiBVc2FnZVJlY29tbWVuZGF0aW9uW10gPSBbXTtcbiAgICBjb25zdCBzdW1tYXJ5ID0gYW5hbHl0aWNzRGF0YT8uc3VtbWFyeTtcbiAgICBjb25zdCBwcm92aWRlcnMgPSBhbmFseXRpY3NEYXRhPy5ncm91cGVkX2RhdGEgfHwgW107XG4gICAgY29uc3QgbW9kZWxzID0gbW9kZWxBbmFseXRpY3M/Lmdyb3VwZWRfZGF0YSB8fCBbXTtcblxuICAgIGlmICghc3VtbWFyeSkgcmV0dXJuIHJlY29tbWVuZGF0aW9ucztcblxuICAgIC8vIENvc3Qgb3B0aW1pemF0aW9uIHJlY29tbWVuZGF0aW9uc1xuICAgIGNvbnN0IG1vc3RFeHBlbnNpdmVNb2RlbCA9IG1vZGVscy5zb3J0KChhLCBiKSA9PiBiLmNvc3QgLSBhLmNvc3QpWzBdO1xuICAgIGlmIChtb3N0RXhwZW5zaXZlTW9kZWwgJiYgbW9zdEV4cGVuc2l2ZU1vZGVsLmNvc3QgPiBzdW1tYXJ5LnRvdGFsX2Nvc3QgKiAwLjMpIHtcbiAgICAgIGNvbnN0IHBvdGVudGlhbFNhdmluZ3MgPSBtb3N0RXhwZW5zaXZlTW9kZWwuY29zdCAqIDAuMjsgLy8gQXNzdW1lIDIwJSBzYXZpbmdzIHBvc3NpYmxlXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCh7XG4gICAgICAgIHR5cGU6ICdjb3N0X29wdGltaXphdGlvbicsXG4gICAgICAgIHRpdGxlOiAnT3B0aW1pemUgRXhwZW5zaXZlIE1vZGVsIFVzYWdlJyxcbiAgICAgICAgZGVzY3JpcHRpb246IGAke21vc3RFeHBlbnNpdmVNb2RlbC5uYW1lfSBhY2NvdW50cyBmb3IgJHsoKG1vc3RFeHBlbnNpdmVNb2RlbC5jb3N0IC8gc3VtbWFyeS50b3RhbF9jb3N0KSAqIDEwMCkudG9GaXhlZCgxKX0lIG9mIHlvdXIgY29zdHMuIENvbnNpZGVyIHVzaW5nIGEgbW9yZSBjb3N0LWVmZmVjdGl2ZSBhbHRlcm5hdGl2ZSBmb3Igc2ltcGxlciB0YXNrcy5gLFxuICAgICAgICBwb3RlbnRpYWxfc2F2aW5nczogcG90ZW50aWFsU2F2aW5ncyxcbiAgICAgICAgaWNvbjogQ3VycmVuY3lEb2xsYXJJY29uXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBQZXJmb3JtYW5jZSByZWNvbW1lbmRhdGlvbnNcbiAgICBpZiAoc3VtbWFyeS5zdWNjZXNzX3JhdGUgPCA5OCkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goe1xuICAgICAgICB0eXBlOiAncGVyZm9ybWFuY2UnLFxuICAgICAgICB0aXRsZTogJ0ltcHJvdmUgUmVxdWVzdCBSZWxpYWJpbGl0eScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgWW91ciBzdWNjZXNzIHJhdGUgaXMgJHtzdW1tYXJ5LnN1Y2Nlc3NfcmF0ZS50b0ZpeGVkKDEpfSUuIEltcGxlbWVudCByZXRyeSBsb2dpYyBhbmQgZXJyb3IgaGFuZGxpbmcgdG8gaW1wcm92ZSByZWxpYWJpbGl0eS5gLFxuICAgICAgICBpY29uOiBDaGVja0NpcmNsZUljb25cbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEVmZmljaWVuY3kgcmVjb21tZW5kYXRpb25zXG4gICAgY29uc3QgYXZnVG9rZW5zUGVyUmVxdWVzdCA9IChzdW1tYXJ5LnRvdGFsX2lucHV0X3Rva2VucyArIHN1bW1hcnkudG90YWxfb3V0cHV0X3Rva2VucykgLyBzdW1tYXJ5LnRvdGFsX3JlcXVlc3RzO1xuICAgIGlmIChhdmdUb2tlbnNQZXJSZXF1ZXN0ID4gMTAwMCkge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goe1xuICAgICAgICB0eXBlOiAnZWZmaWNpZW5jeScsXG4gICAgICAgIHRpdGxlOiAnT3B0aW1pemUgVG9rZW4gVXNhZ2UnLFxuICAgICAgICBkZXNjcmlwdGlvbjogYEF2ZXJhZ2UgJHtmb3JtYXROdW1iZXIoYXZnVG9rZW5zUGVyUmVxdWVzdCl9IHRva2VucyBwZXIgcmVxdWVzdC4gQ29uc2lkZXIgYnJlYWtpbmcgZG93biBsYXJnZSBwcm9tcHRzIG9yIHVzaW5nIG1vcmUgZWZmaWNpZW50IG1vZGVscy5gLFxuICAgICAgICBpY29uOiBDcHVDaGlwSWNvblxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlY29tbWVuZGF0aW9ucztcbiAgfTtcblxuICBjb25zdCByZXNldEZpbHRlcnMgPSAoKSA9PiB7XG4gICAgc2V0VGltZVJhbmdlKCczMCcpO1xuICAgIHNldFNlbGVjdGVkQ29uZmlnKCcnKTtcbiAgICBzZXRTdGFydERhdGUoJycpO1xuICAgIHNldEVuZERhdGUoJycpO1xuICB9O1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzMgbWItNFwiPjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzJcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgIHtbLi4uQXJyYXkoNCldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImNhcmQgcC02IGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS0yMDAgcm91bmRlZCB3LTEvMiBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0zLzQgbWItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1ncmF5LTIwMCByb3VuZGVkIHctMS8zXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgbWItNlwiPvCfk4ogQWR2YW5jZWQgQW5hbHl0aWNzPC9oMT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi00XCI+RXJyb3IgbG9hZGluZyBhbmFseXRpY3M6IHtlcnJvcn08L3A+XG4gICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtmZXRjaEFuYWx5dGljc0RhdGF9IGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCI+XG4gICAgICAgICAgICBSZXRyeVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBjb25zdCBzdW1tYXJ5ID0gYW5hbHl0aWNzRGF0YT8uc3VtbWFyeTtcbiAgY29uc3QgY29zdEVmZmljaWVuY3kgPSBzdW1tYXJ5ID8gc3VtbWFyeS50b3RhbF9jb3N0IC8gTWF0aC5tYXgoc3VtbWFyeS5zdWNjZXNzZnVsX3JlcXVlc3RzLCAxKSA6IDA7XG4gIGNvbnN0IHByb2plY3RlZE1vbnRobHlDb3N0ID0gc3VtbWFyeSA/IChzdW1tYXJ5LnRvdGFsX2Nvc3QgLyBwYXJzZUludCh0aW1lUmFuZ2UpKSAqIDMwIDogMDtcblxuICAvLyBHZW5lcmF0ZSBpbnNpZ2h0c1xuICBjb25zdCBjb3N0QWxlcnRzID0gZ2VuZXJhdGVDb3N0QWxlcnRzKCk7XG4gIGNvbnN0IHJlY29tbWVuZGF0aW9ucyA9IGdlbmVyYXRlUmVjb21tZW5kYXRpb25zKCk7XG5cbiAgLy8gQ2FsY3VsYXRlIHRyZW5kcyB2cyBwcmV2aW91cyBwZXJpb2RcbiAgY29uc3QgcHJldmlvdXNTdW1tYXJ5ID0gcHJldmlvdXNQZXJpb2REYXRhPy5zdW1tYXJ5O1xuICBjb25zdCBjb3N0VHJlbmQgPSBwcmV2aW91c1N1bW1hcnkgPyBjYWxjdWxhdGVUcmVuZChzdW1tYXJ5Py50b3RhbF9jb3N0IHx8IDAsIHByZXZpb3VzU3VtbWFyeS50b3RhbF9jb3N0KSA6IG51bGw7XG4gIGNvbnN0IHJlcXVlc3RUcmVuZCA9IHByZXZpb3VzU3VtbWFyeSA/IGNhbGN1bGF0ZVRyZW5kKHN1bW1hcnk/LnRvdGFsX3JlcXVlc3RzIHx8IDAsIHByZXZpb3VzU3VtbWFyeS50b3RhbF9yZXF1ZXN0cykgOiBudWxsO1xuXG4gIC8vIFByb2Zlc3Npb25hbCBMaW5lIENoYXJ0IENvbXBvbmVudCAobGlrZSByZWZlcmVuY2UpXG4gIGNvbnN0IFByb2Zlc3Npb25hbExpbmVDaGFydCA9ICh7IGRhdGEgfTogeyBkYXRhOiBUaW1lU2VyaWVzRGF0YVtdIH0pID0+IHtcbiAgICBpZiAoIWRhdGEubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNjQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8Q2hhcnRCYXJJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Tm8gZGF0YSBhdmFpbGFibGU8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCBtYXhDb3N0ID0gTWF0aC5tYXgoLi4uZGF0YS5tYXAoZCA9PiBkLmNvc3QpKTtcbiAgICBjb25zdCBtaW5Db3N0ID0gTWF0aC5taW4oLi4uZGF0YS5tYXAoZCA9PiBkLmNvc3QpKTtcbiAgICBjb25zdCBtYXhSZXF1ZXN0cyA9IE1hdGgubWF4KC4uLmRhdGEubWFwKGQgPT4gZC5yZXF1ZXN0cykpO1xuICAgIGNvbnN0IG1pblJlcXVlc3RzID0gTWF0aC5taW4oLi4uZGF0YS5tYXAoZCA9PiBkLnJlcXVlc3RzKSk7XG5cbiAgICBjb25zdCBjb3N0UmFuZ2UgPSBtYXhDb3N0IC0gbWluQ29zdCB8fCAxO1xuICAgIGNvbnN0IHJlcXVlc3RSYW5nZSA9IG1heFJlcXVlc3RzIC0gbWluUmVxdWVzdHMgfHwgMTtcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGgtNjQgdy1mdWxsXCI+XG4gICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiIHZpZXdCb3g9XCIwIDAgNjAwIDIwMFwiPlxuICAgICAgICAgIDxkZWZzPlxuICAgICAgICAgICAgey8qIEdyYWRpZW50cyBmb3Igc21vb3RoIGZpbGxzICovfVxuICAgICAgICAgICAgPGxpbmVhckdyYWRpZW50IGlkPVwiY29zdEdyYWRpZW50XCIgeDE9XCIwJVwiIHkxPVwiMCVcIiB4Mj1cIjAlXCIgeTI9XCIxMDAlXCI+XG4gICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjAlXCIgc3RvcENvbG9yPVwiIzhiNWNmNlwiIHN0b3BPcGFjaXR5PVwiMC4zXCIgLz5cbiAgICAgICAgICAgICAgPHN0b3Agb2Zmc2V0PVwiMTAwJVwiIHN0b3BDb2xvcj1cIiM4YjVjZjZcIiBzdG9wT3BhY2l0eT1cIjAuMDVcIiAvPlxuICAgICAgICAgICAgPC9saW5lYXJHcmFkaWVudD5cbiAgICAgICAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD1cInJlcXVlc3RHcmFkaWVudFwiIHgxPVwiMCVcIiB5MT1cIjAlXCIgeDI9XCIwJVwiIHkyPVwiMTAwJVwiPlxuICAgICAgICAgICAgICA8c3RvcCBvZmZzZXQ9XCIwJVwiIHN0b3BDb2xvcj1cIiMwNmI2ZDRcIiBzdG9wT3BhY2l0eT1cIjAuM1wiIC8+XG4gICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjEwMCVcIiBzdG9wQ29sb3I9XCIjMDZiNmQ0XCIgc3RvcE9wYWNpdHk9XCIwLjA1XCIgLz5cbiAgICAgICAgICAgIDwvbGluZWFyR3JhZGllbnQ+XG5cbiAgICAgICAgICAgIHsvKiBTdWJ0bGUgZ3JpZCBwYXR0ZXJuICovfVxuICAgICAgICAgICAgPHBhdHRlcm4gaWQ9XCJncmlkXCIgd2lkdGg9XCI0MFwiIGhlaWdodD1cIjQwXCIgcGF0dGVyblVuaXRzPVwidXNlclNwYWNlT25Vc2VcIj5cbiAgICAgICAgICAgICAgPHBhdGggZD1cIk0gNDAgMCBMIDAgMCAwIDQwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCIjZjFmNWY5XCIgc3Ryb2tlV2lkdGg9XCIwLjVcIi8+XG4gICAgICAgICAgICA8L3BhdHRlcm4+XG4gICAgICAgICAgPC9kZWZzPlxuXG4gICAgICAgICAgey8qIEdyaWQgYmFja2dyb3VuZCAqL31cbiAgICAgICAgICA8cmVjdCB3aWR0aD1cIjYwMFwiIGhlaWdodD1cIjIwMFwiIGZpbGw9XCJ1cmwoI2dyaWQpXCIgLz5cblxuICAgICAgICAgIHsvKiBDb3N0IGFyZWEgZmlsbCAqL31cbiAgICAgICAgICA8cG9seWdvblxuICAgICAgICAgICAgZmlsbD1cInVybCgjY29zdEdyYWRpZW50KVwiXG4gICAgICAgICAgICBwb2ludHM9e2AwLDIwMCAke2RhdGEubWFwKChkLCBpKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHggPSAoaSAvIE1hdGgubWF4KGRhdGEubGVuZ3RoIC0gMSwgMSkpICogNjAwO1xuICAgICAgICAgICAgICBjb25zdCB5ID0gMjAwIC0gKChkLmNvc3QgLSBtaW5Db3N0KSAvIGNvc3RSYW5nZSkgKiAxNjA7XG4gICAgICAgICAgICAgIHJldHVybiBgJHt4fSwke3l9YDtcbiAgICAgICAgICAgIH0pLmpvaW4oJyAnKX0gNjAwLDIwMGB9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIHsvKiBSZXF1ZXN0IGFyZWEgZmlsbCAqL31cbiAgICAgICAgICA8cG9seWdvblxuICAgICAgICAgICAgZmlsbD1cInVybCgjcmVxdWVzdEdyYWRpZW50KVwiXG4gICAgICAgICAgICBwb2ludHM9e2AwLDIwMCAke2RhdGEubWFwKChkLCBpKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHggPSAoaSAvIE1hdGgubWF4KGRhdGEubGVuZ3RoIC0gMSwgMSkpICogNjAwO1xuICAgICAgICAgICAgICBjb25zdCB5ID0gMjAwIC0gKChkLnJlcXVlc3RzIC0gbWluUmVxdWVzdHMpIC8gcmVxdWVzdFJhbmdlKSAqIDE2MDtcbiAgICAgICAgICAgICAgcmV0dXJuIGAke3h9LCR7eX1gO1xuICAgICAgICAgICAgfSkuam9pbignICcpfSA2MDAsMjAwYH1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgey8qIENvc3QgbGluZSAtIFB1cnBsZSBsaWtlIHJlZmVyZW5jZSAqL31cbiAgICAgICAgICA8cG9seWxpbmVcbiAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgIHN0cm9rZT1cIiM4YjVjZjZcIlxuICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyLjVcIlxuICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgcG9pbnRzPXtkYXRhLm1hcCgoZCwgaSkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCB4ID0gKGkgLyBNYXRoLm1heChkYXRhLmxlbmd0aCAtIDEsIDEpKSAqIDYwMDtcbiAgICAgICAgICAgICAgY29uc3QgeSA9IDIwMCAtICgoZC5jb3N0IC0gbWluQ29zdCkgLyBjb3N0UmFuZ2UpICogMTYwO1xuICAgICAgICAgICAgICByZXR1cm4gYCR7eH0sJHt5fWA7XG4gICAgICAgICAgICB9KS5qb2luKCcgJyl9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIHsvKiBSZXF1ZXN0IGxpbmUgLSBDeWFuIGxpa2UgcmVmZXJlbmNlICovfVxuICAgICAgICAgIDxwb2x5bGluZVxuICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgc3Ryb2tlPVwiIzA2YjZkNFwiXG4gICAgICAgICAgICBzdHJva2VXaWR0aD1cIjIuNVwiXG4gICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICBwb2ludHM9e2RhdGEubWFwKChkLCBpKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHggPSAoaSAvIE1hdGgubWF4KGRhdGEubGVuZ3RoIC0gMSwgMSkpICogNjAwO1xuICAgICAgICAgICAgICBjb25zdCB5ID0gMjAwIC0gKChkLnJlcXVlc3RzIC0gbWluUmVxdWVzdHMpIC8gcmVxdWVzdFJhbmdlKSAqIDE2MDtcbiAgICAgICAgICAgICAgcmV0dXJuIGAke3h9LCR7eX1gO1xuICAgICAgICAgICAgfSkuam9pbignICcpfVxuICAgICAgICAgIC8+XG5cbiAgICAgICAgICB7LyogRGF0YSBwb2ludHMgd2l0aCBob3ZlciBlZmZlY3RzICovfVxuICAgICAgICAgIHtkYXRhLm1hcCgoZCwgaSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgeCA9IChpIC8gTWF0aC5tYXgoZGF0YS5sZW5ndGggLSAxLCAxKSkgKiA2MDA7XG4gICAgICAgICAgICBjb25zdCBjb3N0WSA9IDIwMCAtICgoZC5jb3N0IC0gbWluQ29zdCkgLyBjb3N0UmFuZ2UpICogMTYwO1xuICAgICAgICAgICAgY29uc3QgcmVxdWVzdFkgPSAyMDAgLSAoKGQucmVxdWVzdHMgLSBtaW5SZXF1ZXN0cykgLyByZXF1ZXN0UmFuZ2UpICogMTYwO1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPGcga2V5PXtpfT5cbiAgICAgICAgICAgICAgICA8Y2lyY2xlXG4gICAgICAgICAgICAgICAgICBjeD17eH1cbiAgICAgICAgICAgICAgICAgIGN5PXtjb3N0WX1cbiAgICAgICAgICAgICAgICAgIHI9XCIzXCJcbiAgICAgICAgICAgICAgICAgIGZpbGw9XCIjOGI1Y2Y2XCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZT1cIndoaXRlXCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpyLTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8dGl0bGU+e2Ake2QucGVyaW9kfTogJHtmb3JtYXRDdXJyZW5jeShkLmNvc3QpfWB9PC90aXRsZT5cbiAgICAgICAgICAgICAgICA8L2NpcmNsZT5cbiAgICAgICAgICAgICAgICA8Y2lyY2xlXG4gICAgICAgICAgICAgICAgICBjeD17eH1cbiAgICAgICAgICAgICAgICAgIGN5PXtyZXF1ZXN0WX1cbiAgICAgICAgICAgICAgICAgIHI9XCIzXCJcbiAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMDZiNmQ0XCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZT1cIndoaXRlXCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpyLTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8dGl0bGU+e2Ake2QucGVyaW9kfTogJHtmb3JtYXROdW1iZXIoZC5yZXF1ZXN0cyl9IHJlcXVlc3RzYH08L3RpdGxlPlxuICAgICAgICAgICAgICAgIDwvY2lyY2xlPlxuICAgICAgICAgICAgICA8L2c+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0pfVxuICAgICAgICA8L3N2Zz5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gUHJvZmVzc2lvbmFsIERvbnV0IENoYXJ0IENvbXBvbmVudCAobGlrZSByZWZlcmVuY2UpXG4gIGNvbnN0IFByb2Zlc3Npb25hbERvbnV0Q2hhcnQgPSAoeyBkYXRhLCB0b3RhbCB9OiB7IGRhdGE6IGFueVtdLCB0b3RhbDogbnVtYmVyIH0pID0+IHtcbiAgICBpZiAoIWRhdGEubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNDggZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8Q2hhcnRQaWVJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Tm8gZGF0YSBhdmFpbGFibGU8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBQcm9mZXNzaW9uYWwgY29sb3IgcGFsZXR0ZSBsaWtlIHRoZSByZWZlcmVuY2VcbiAgICBjb25zdCBjb2xvcnMgPSBbJyNmNDNmNWUnLCAnIzhiNWNmNicsICcjMDZiNmQ0JywgJyMxMGI5ODEnLCAnI2Y1OWUwYicsICcjZWY0NDQ0J107XG4gICAgY29uc3QgcmFkaXVzID0gNzA7XG4gICAgY29uc3Qgc3Ryb2tlV2lkdGggPSAxNjtcbiAgICBjb25zdCBub3JtYWxpemVkUmFkaXVzID0gcmFkaXVzIC0gc3Ryb2tlV2lkdGggKiAwLjU7XG4gICAgY29uc3QgY2lyY3VtZmVyZW5jZSA9IG5vcm1hbGl6ZWRSYWRpdXMgKiAyICogTWF0aC5QSTtcblxuICAgIGxldCBjdW11bGF0aXZlUGVyY2VudGFnZSA9IDA7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1iLTZcIj5cbiAgICAgICAgICA8c3ZnIHdpZHRoPVwiMTYwXCIgaGVpZ2h0PVwiMTYwXCIgY2xhc3NOYW1lPVwidHJhbnNmb3JtIC1yb3RhdGUtOTBcIj5cbiAgICAgICAgICAgIHsvKiBCYWNrZ3JvdW5kIGNpcmNsZSB3aXRoIHN1YnRsZSBzaGFkb3cgKi99XG4gICAgICAgICAgICA8Y2lyY2xlXG4gICAgICAgICAgICAgIGN4PVwiODBcIlxuICAgICAgICAgICAgICBjeT1cIjgwXCJcbiAgICAgICAgICAgICAgcj17bm9ybWFsaXplZFJhZGl1c31cbiAgICAgICAgICAgICAgc3Ryb2tlPVwiI2Y4ZmFmY1wiXG4gICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXtzdHJva2VXaWR0aH1cbiAgICAgICAgICAgICAgZmlsbD1cInRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgIHsvKiBEYXRhIHNlZ21lbnRzICovfVxuICAgICAgICAgICAge2RhdGEubWFwKChpdGVtLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBwZXJjZW50YWdlID0gKGl0ZW0uY29zdCAvIHRvdGFsKSAqIDEwMDtcbiAgICAgICAgICAgICAgY29uc3Qgc3Ryb2tlRGFzaGFycmF5ID0gYCR7KHBlcmNlbnRhZ2UgLyAxMDApICogY2lyY3VtZmVyZW5jZX0gJHtjaXJjdW1mZXJlbmNlfWA7XG4gICAgICAgICAgICAgIGNvbnN0IHN0cm9rZURhc2hvZmZzZXQgPSAtKChjdW11bGF0aXZlUGVyY2VudGFnZSAvIDEwMCkgKiBjaXJjdW1mZXJlbmNlKTtcblxuICAgICAgICAgICAgICBjdW11bGF0aXZlUGVyY2VudGFnZSArPSBwZXJjZW50YWdlO1xuXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGNpcmNsZVxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgIGN4PVwiODBcIlxuICAgICAgICAgICAgICAgICAgY3k9XCI4MFwiXG4gICAgICAgICAgICAgICAgICByPXtub3JtYWxpemVkUmFkaXVzfVxuICAgICAgICAgICAgICAgICAgc3Ryb2tlPXtjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXX1cbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXtzdHJva2VXaWR0aH1cbiAgICAgICAgICAgICAgICAgIHN0cm9rZURhc2hhcnJheT17c3Ryb2tlRGFzaGFycmF5fVxuICAgICAgICAgICAgICAgICAgc3Ryb2tlRGFzaG9mZnNldD17c3Ryb2tlRGFzaG9mZnNldH1cbiAgICAgICAgICAgICAgICAgIGZpbGw9XCJ0cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOm9wYWNpdHktODAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiAnZHJvcC1zaGFkb3coMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKSknXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L3N2Zz5cblxuICAgICAgICAgIHsvKiBDbGVhbiBjZW50ZXIgLSBubyB1Z2x5IGNvc3QgZGlzcGxheSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntkYXRhLmxlbmd0aH08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5Qcm92aWRlcnM8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ2xlYW4gTGVnZW5kICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB3LWZ1bGxcIj5cbiAgICAgICAgICB7ZGF0YS5zbGljZSgwLCA0KS5tYXAoKGl0ZW0sIGluZGV4KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBwZXJjZW50YWdlID0gKChpdGVtLmNvc3QgLyB0b3RhbCkgKiAxMDApLnRvRml4ZWQoMSk7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweS0yIHB4LTMgcm91bmRlZC1sZyBob3ZlcjpiZy13aGl0ZS81IHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTE1MFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgbXItMyBzaGFkb3ctc21cIlxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9yc1tpbmRleF0gfX1cbiAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBjYXBpdGFsaXplXCI+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+e3BlcmNlbnRhZ2V9JTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj57Zm9ybWF0Q3VycmVuY3koaXRlbS5jb3N0KX08L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0pfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1bIzA0MDcxNl0gcC02XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+QW5hbHl0aWNzPC9oMT5cblxuICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIFRhYnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgbWItNlwiPlxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctY3lhbi01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgT3ZlcnZpZXdcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgVXNlcnNcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgRXJyb3JzXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgIENhY2hlXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgIEZlZWRiYWNrXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgIE1ldGFkYXRhXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTZWFyY2ggYW5kIERhdGUgRmlsdGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBGaWx0ZXJcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctWyMxQjFDMURdIGJvcmRlciBib3JkZXItZ3JheS03MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHB4LTQgcHktMiBwbC0xMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctY3lhbi01MDAgdy02NFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMi41IGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTIxIDIxbC02LTZtMi01YTcgNyAwIDExLTE0IDAgNyA3IDAgMDExNCAwelwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3RpbWVSYW5nZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFRpbWVSYW5nZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctWyMxQjFDMURdIGJvcmRlciBib3JkZXItZ3JheS03MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHB4LTQgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctY3lhbi01MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjdcIj4yMDIzLTA3LTA2IC0gRGVjIDIwMjM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMzBcIj5MYXN0IDMwIGRheXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiOTBcIj5MYXN0IDkwIGRheXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFRvcCBNZXRyaWMgQ2FyZHMgLSBMaWtlIE44TiBSZWZlcmVuY2UgKi99XG4gICAgICAgIHtzdW1tYXJ5ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgICAgey8qIFRvdGFsIFJlcXVlc3QgTWFkZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMxQjFDMURdIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwIG1iLTFcIj5Ub3RhbCBSZXF1ZXN0IE1hZGU8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihzdW1tYXJ5LnRvdGFsX3JlcXVlc3RzKX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7cmVxdWVzdFRyZW5kICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RUcmVuZC5pc1Bvc2l0aXZlID8gJ2JnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMCcgOiAnYmctcmVkLTUwMC8yMCB0ZXh0LXJlZC00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3JlcXVlc3RUcmVuZC5pc1Bvc2l0aXZlID8gJysnIDogJyd9e3JlcXVlc3RUcmVuZC5wZXJjZW50YWdlLnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctYmx1ZS01MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPENoYXJ0QmFySWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQXZlcmFnZSBMYXRlbmN5ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzFCMUMxRF0gYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDAgbWItMVwiPkF2ZXJhZ2UgTGF0ZW5jeTwvaDM+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7c3VtbWFyeT8uYXZlcmFnZV9sYXRlbmN5ID8gYCR7c3VtbWFyeS5hdmVyYWdlX2xhdGVuY3kudG9GaXhlZCgwKX1tc2AgOiAnMTQ0bXMnfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSBiZy1vcmFuZ2UtNTAwLzIwIHRleHQtb3JhbmdlLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICsyMC4zNCVcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1vcmFuZ2UtNTAwLzIwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxDbG9ja0ljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LW9yYW5nZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogVXNlciBGZWVkYmFjayAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMxQjFDMURdIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwIG1iLTFcIj5Vc2VyIEZlZWRiYWNrPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbXItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdW1tYXJ5LnN1Y2Nlc3NfcmF0ZS50b0ZpeGVkKDEpfSVcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICBzdW1tYXJ5LnN1Y2Nlc3NfcmF0ZSA+PSA5NSA/ICdiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDAnIDogJ2JnLXJlZC01MDAvMjAgdGV4dC1yZWQtNDAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge3N1bW1hcnkuc3VjY2Vzc19yYXRlID49IDk1ID8gJysxLjM0JScgOiAnLTEuMzQlJ31cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmVlbi01MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPEhhbmRUaHVtYlVwSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFRvdGFsIENvc3QgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjMUIxQzFEXSBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMCBtYi0xXCI+Q29zdDwvaDM+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koc3VtbWFyeS50b3RhbF9jb3N0KX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7Y29zdFRyZW5kICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvc3RUcmVuZC5pc1Bvc2l0aXZlID8gJ2JnLXJlZC01MDAvMjAgdGV4dC1yZWQtNDAwJyA6ICdiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2Nvc3RUcmVuZC5pc1Bvc2l0aXZlID8gJysnIDogJyd9e2Nvc3RUcmVuZC5wZXJjZW50YWdlLnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctcHVycGxlLTUwMC8yMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICA8Q3VycmVuY3lEb2xsYXJJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wdXJwbGUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogQ2hhcnRzIFNlY3Rpb24gLSA2IENoYXJ0IEdyaWQgTGlrZSBOOE4gUmVmZXJlbmNlICovfVxuICAgICAgICB7LyogNi1DaGFydCBHcmlkIExheW91dCBMaWtlIE44TiBSZWZlcmVuY2UgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICAgIHsvKiBDb3N0IENoYXJ0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMxQjFDMURdIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPkNvc3Q8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbXItM1wiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koc3VtbWFyeT8udG90YWxfY29zdCB8fCA5ODQ5Mil9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gYmctcmVkLTUwMC8yMCB0ZXh0LXJlZC00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgLTEwLjQ2JVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ncmF5LTcwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS00MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMyA3aDhtMCAwdjhtMC04bC04IDgtNC00LTYgNlwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7LyogQmFyIENoYXJ0ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMyIGZsZXggaXRlbXMtZW5kIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICB7Wy4uLkFycmF5KDIwKV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAwIHJvdW5kZWQtdFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IGAke01hdGgucmFuZG9tKCkgKiA4MCArIDIwfSVgLFxuICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzQlJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIExhdGVuY3kgQ2hhcnQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzFCMUMxRF0gYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+TGF0ZW5jeTwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzdW1tYXJ5Py5hdmVyYWdlX2xhdGVuY3kgPyBgJHtzdW1tYXJ5LmF2ZXJhZ2VfbGF0ZW5jeS50b0ZpeGVkKDApfW1zYCA6ICcyOW1zJ31cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSBiZy1vcmFuZ2UtNTAwLzIwIHRleHQtb3JhbmdlLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAxMTIlXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLWdyYXktNzAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICA8Q2xvY2tJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7LyogTGluZSBDaGFydCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zMiByZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgZD1cIk0wLDgwIFE1MCw2MCAxMDAsNzAgVDIwMCw1MCBUMzAwLDYwXCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZT1cIiNmNTllMGJcIlxuICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcbiAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFRva2VucyBVc2VkIENoYXJ0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMxQjFDMURdIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPlRva2VucyBVc2VkPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5NYXJjaCAyODwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1yLTNcIj5cbiAgICAgICAgICAgICAgICAgICAge3N1bW1hcnkgPyBgJHsoKHN1bW1hcnkudG90YWxfaW5wdXRfdG9rZW5zICsgc3VtbWFyeS50b3RhbF9vdXRwdXRfdG9rZW5zKSAvIDEwMDAwMDApLnRvRml4ZWQoMSl9TWAgOiAnMjUuNE0nfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICArOS4zOSVcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctZ3JheS03MDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgIDxDcHVDaGlwSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgey8qIFNjYXR0ZXIgUGxvdCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zMiByZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDMwKV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8Y2lyY2xlXG4gICAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgICAgY3g9e01hdGgucmFuZG9tKCkgKiAyODAgKyAxMH1cbiAgICAgICAgICAgICAgICAgICAgY3k9e01hdGgucmFuZG9tKCkgKiAxMDAgKyAxMH1cbiAgICAgICAgICAgICAgICAgICAgcj1cIjJcIlxuICAgICAgICAgICAgICAgICAgICBmaWxsPXtbJyMxMGI5ODEnLCAnIzNiODJmNicsICcjZjU5ZTBiJ11bTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMyldfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBSZXF1ZXN0cyBDaGFydCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjMUIxQzFEXSBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5SZXF1ZXN0czwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIoc3VtbWFyeT8udG90YWxfcmVxdWVzdHMgfHwgNjM4MDApfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICArMy4zOSVcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctZ3JheS03MDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgIDxDaGFydEJhckljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBIb3Jpem9udGFsIEJhciBDaGFydCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtbLi4uQXJyYXkoNildLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1waW5rLTUwMCB0by1ncmVlbi01MDAgaC0yIHJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgucmFuZG9tKCkgKiA4MCArIDIwfSVgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBVbmlxdWUgVXNlcnMgQ2hhcnQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzFCMUMxRF0gYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+VW5pcXVlIFVzZXJzPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1yLTNcIj43ODwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSBiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgKzMuMzklXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLWdyYXktNzAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDQuMzU0YTQgNCAwIDExMCA1LjI5Mk0xNSAyMUgzdi0xYTYgNiAwIDAxMTIgMHYxem0wIDBoNnYtMWE2IDYgMCAwMC05LTUuMTk3bTEzLjUtOWEyLjUgMi41IDAgMTEtNSAwIDIuNSAyLjUgMCAwMTUgMHpcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgey8qIFdhdmUgQ2hhcnQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMzIgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgIGQ9XCJNMCw4MCBRNTAsNDAgMTAwLDYwIFQyMDAsNTAgVDMwMCw3MFwiXG4gICAgICAgICAgICAgICAgICBzdHJva2U9XCIjOGI1Y2Y2XCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXG4gICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJlbW92ZSBkdXBsaWNhdGUgbWV0cmljIGNhcmRzIHNlY3Rpb24gKi99XG4gICAgICAgIHtzdW1tYXJ5ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgICAgey8qIFRvdGFsIFJlcXVlc3RzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzJBMkIyQ10gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLTJ4bCBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwIG1iLTFcIj5Ub3RhbCBSZXF1ZXN0IE1hZGU8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihzdW1tYXJ5LnRvdGFsX3JlcXVlc3RzKX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7cmVxdWVzdFRyZW5kICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3RUcmVuZC5pc1Bvc2l0aXZlID8gJ2JnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMCcgOiAnYmctcmVkLTUwMC8yMCB0ZXh0LXJlZC00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3JlcXVlc3RUcmVuZC5pc1Bvc2l0aXZlID8gJysnIDogJyd9e3JlcXVlc3RUcmVuZC5wZXJjZW50YWdlLnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctYmx1ZS01MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPEFycm93UGF0aEljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEF2ZXJhZ2UgTGF0ZW5jeSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMyQTJCMkNdIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC0yeGwgcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMCBtYi0xXCI+QXZlcmFnZSBMYXRlbmN5PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbXItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdW1tYXJ5LmF2ZXJhZ2VfbGF0ZW5jeSA/IGAke3N1bW1hcnkuYXZlcmFnZV9sYXRlbmN5LnRvRml4ZWQoMCl9bXNgIDogJzBtcyd9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLW9yYW5nZS01MDAvMjAgdGV4dC1vcmFuZ2UtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgKzIwLjM0JVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLW9yYW5nZS01MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPENsb2NrSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtb3JhbmdlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBVc2VyIEZlZWRiYWNrICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzJBMkIyQ10gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLTJ4bCBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwIG1iLTFcIj5Vc2VyIEZlZWRiYWNrPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbXItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdW1tYXJ5LnN1Y2Nlc3NfcmF0ZS50b0ZpeGVkKDEpfSVcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICBzdW1tYXJ5LnN1Y2Nlc3NfcmF0ZSA+PSA5NSA/ICdiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDAnIDogJ2JnLXJlZC01MDAvMjAgdGV4dC1yZWQtNDAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge3N1bW1hcnkuc3VjY2Vzc19yYXRlID49IDk1ID8gJysxLjM0JScgOiAnLTEuMzQlJ31cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmVlbi01MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPEhhbmRUaHVtYlVwSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFRvdGFsIENvc3QgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjMkEyQjJDXSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDAgbWItMVwiPlRvdGFsIENvc3Q8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdEN1cnJlbmN5KHN1bW1hcnkudG90YWxfY29zdCl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAge2Nvc3RUcmVuZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb3N0VHJlbmQuaXNQb3NpdGl2ZSA/ICdiZy1yZWQtNTAwLzIwIHRleHQtcmVkLTQwMCcgOiAnYmctZ3JlZW4tNTAwLzIwIHRleHQtZ3JlZW4tNDAwJ1xuICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb3N0VHJlbmQuaXNQb3NpdGl2ZSA/ICcrJyA6ICcnfXtjb3N0VHJlbmQucGVyY2VudGFnZS50b0ZpeGVkKDEpfSVcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLXB1cnBsZS01MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPEN1cnJlbmN5RG9sbGFySWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtcHVycGxlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgICB7LyogQWRkaXRpb25hbCBNZXRyaWNzIC0gTjhOIFN0eWxlICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgIHsvKiBDb3N0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMyQTJCMkNdIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC0yeGwgcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMCBtYi0xXCI+Q29zdDwvaDM+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koc3VtbWFyeT8udG90YWxfY29zdCB8fCAwKX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLXJlZC01MDAvMjAgdGV4dC1yZWQtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAxMC40NiVcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBTaW1wbGUgYmFyIGNoYXJ0IHJlcHJlc2VudGF0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5UaGlzIHBlcmlvZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+e2Zvcm1hdEN1cnJlbmN5KHN1bW1hcnk/LnRvdGFsX2Nvc3QgfHwgMCl9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAgcm91bmRlZC1mdWxsIGgtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAwIGgtMiByb3VuZGVkLWZ1bGxcIiBzdHlsZT17eyB3aWR0aDogJzc1JScgfX0+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTGF0ZW5jeSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjMkEyQjJDXSBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDAgbWItMVwiPkxhdGVuY3k8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbXItM1wiPlxuICAgICAgICAgICAgICAgICAge3N1bW1hcnk/LmF2ZXJhZ2VfbGF0ZW5jeSA/IGAke3N1bW1hcnkuYXZlcmFnZV9sYXRlbmN5LnRvRml4ZWQoMCl9bXNgIDogJzBtcyd9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSBiZy1vcmFuZ2UtNTAwLzIwIHRleHQtb3JhbmdlLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgMTEyJVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgey8qIFNpbXBsZSBsaW5lIGNoYXJ0IHJlcHJlc2VudGF0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5BdmVyYWdlIHJlc3BvbnNlPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj57c3VtbWFyeT8uYXZlcmFnZV9sYXRlbmN5ID8gYCR7c3VtbWFyeS5hdmVyYWdlX2xhdGVuY3kudG9GaXhlZCgwKX1tc2AgOiAnMG1zJ308L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNTAwIGgtMiByb3VuZGVkLWZ1bGxcIiBzdHlsZT17eyB3aWR0aDogJzYwJScgfX0+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogVG9rZW5zIFVzZWQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzJBMkIyQ10gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLTJ4bCBwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwIG1iLTFcIj5Ub2tlbnMgVXNlZDwvaDM+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICB7c3VtbWFyeSA/IGAkeygoc3VtbWFyeS50b3RhbF9pbnB1dF90b2tlbnMgKyBzdW1tYXJ5LnRvdGFsX291dHB1dF90b2tlbnMpIC8gMTAwMDAwMCkudG9GaXhlZCgxKX1NYCA6ICcwTSd9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSBiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDBcIj5cbiAgICAgICAgICAgICAgICAgICs5LjM5JVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgey8qIFRva2VuIHVzYWdlIGJyZWFrZG93biAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+VG90YWwgcHJvY2Vzc2VkPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj57c3VtbWFyeSA/IGZvcm1hdE51bWJlcihzdW1tYXJ5LnRvdGFsX2lucHV0X3Rva2VucyArIHN1bW1hcnkudG90YWxfb3V0cHV0X3Rva2VucykgOiAnMCd9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAgcm91bmRlZC1mdWxsIGgtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHVycGxlLTUwMCBoLTIgcm91bmRlZC1mdWxsXCIgc3R5bGU9e3sgd2lkdGg6ICc4NSUnIH19PjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRGV0YWlsZWQgQW5hbHl0aWNzIFRhYmxlcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC02IG1iLThcIj5cbiAgICAgICAgICB7LyogUHJvdmlkZXIgUGVyZm9ybWFuY2UgVGFibGUgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzJBMkIyQ10gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLTJ4bCBwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMVwiPlByb3ZpZGVyIFBlcmZvcm1hbmNlPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+RGV0YWlsZWQgYnJlYWtkb3duIGJ5IHByb3ZpZGVyPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7YW5hbHl0aWNzRGF0YT8uZ3JvdXBlZF9kYXRhLmxlbmd0aCA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICA8dGhlYWQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ciBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItd2hpdGUvMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwXCI+UHJvdmlkZXI8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHB5LTMgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNDAwXCI+Q29zdDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgcHktMyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDBcIj5SZXF1ZXN0czwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgcHktMyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDBcIj5TaGFyZTwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImRpdmlkZS15IGRpdmlkZS13aGl0ZS81XCI+XG4gICAgICAgICAgICAgICAgICAgIHthbmFseXRpY3NEYXRhLmdyb3VwZWRfZGF0YVxuICAgICAgICAgICAgICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLmNvc3QgLSBhLmNvc3QpXG4gICAgICAgICAgICAgICAgICAgICAgLm1hcCgocHJvdmlkZXIsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb2xvcnMgPSBbJyNmZjZiMzUnLCAnIzNiODJmNicsICcjMTBiOTgxJywgJyNmNTllMGInLCAnI2VmNDQ0NCcsICcjOGI1Y2Y2J107XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwZXJjZW50YWdlID0gKChwcm92aWRlci5jb3N0IC8gKHN1bW1hcnk/LnRvdGFsX2Nvc3QgfHwgMSkpICogMTAwKS50b0ZpeGVkKDEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17cHJvdmlkZXIubmFtZX0gY2xhc3NOYW1lPVwiaG92ZXI6Ymctd2hpdGUvNSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0xNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgbXItM1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXSB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtd2hpdGUgY2FwaXRhbGl6ZVwiPntwcm92aWRlci5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTQgdGV4dC1yaWdodCBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShwcm92aWRlci5jb3N0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS00IHRleHQtcmlnaHQgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihwcm92aWRlci5yZXF1ZXN0cyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktNCB0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMi41IHB5LTAuNSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBiZy13aGl0ZS8xMCB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwZXJjZW50YWdlfSVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTggdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIDxDaGFydFBpZUljb24gY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG14LWF1dG8gbWItNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICA8cD5ObyBwcm92aWRlciBkYXRhIGF2YWlsYWJsZTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE1vZGVsIFBlcmZvcm1hbmNlIFRhYmxlICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMyQTJCMkNdIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC0yeGwgcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj5Ub3AgTW9kZWxzPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+TW9zdCBleHBlbnNpdmUgbW9kZWxzIGJ5IGNvc3Q8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge21vZGVsQW5hbHl0aWNzPy5ncm91cGVkX2RhdGEubGVuZ3RoID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgIDx0aGVhZD5cbiAgICAgICAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci13aGl0ZS8xMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDBcIj5Nb2RlbDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgcHktMyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDBcIj5Db3N0PC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1yaWdodCBweS0zIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMFwiPlJlcXVlc3RzPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1yaWdodCBweS0zIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMFwiPkF2Zy9SZXF1ZXN0PC90aD5cbiAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLXdoaXRlLzVcIj5cbiAgICAgICAgICAgICAgICAgICAge21vZGVsQW5hbHl0aWNzLmdyb3VwZWRfZGF0YVxuICAgICAgICAgICAgICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLmNvc3QgLSBhLmNvc3QpXG4gICAgICAgICAgICAgICAgICAgICAgLnNsaWNlKDAsIDUpXG4gICAgICAgICAgICAgICAgICAgICAgLm1hcCgobW9kZWwsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb2xvcnMgPSBbJyNmZjZiMzUnLCAnIzNiODJmNicsICcjMTBiOTgxJywgJyNmNTllMGInLCAnI2VmNDQ0NCddO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYXZnQ29zdCA9IG1vZGVsLmNvc3QgLyBNYXRoLm1heChtb2RlbC5yZXF1ZXN0cywgMSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dHIga2V5PXttb2RlbC5uYW1lfSBjbGFzc05hbWU9XCJob3ZlcjpiZy13aGl0ZS81IHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTE1MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbCBtci0zXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9yc1tpbmRleCAlIGNvbG9ycy5sZW5ndGhdIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+e21vZGVsLm5hbWUubGVuZ3RoID4gMjAgPyBtb2RlbC5uYW1lLnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyA6IG1vZGVsLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIobW9kZWwuaW5wdXRfdG9rZW5zICsgbW9kZWwub3V0cHV0X3Rva2Vucyl9IHRva2Vuc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTQgdGV4dC1yaWdodCBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShtb2RlbC5jb3N0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweS00IHRleHQtcmlnaHQgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihtb2RlbC5yZXF1ZXN0cyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktNCB0ZXh0LXJpZ2h0IHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShhdmdDb3N0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTggdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIDxDcHVDaGlwSWNvbiBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byBtYi00IHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICAgIDxwPk5vIG1vZGVsIGRhdGEgYXZhaWxhYmxlPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG5cblxuXG5cblxuXG5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBbmFseXRpY3NQYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMS8zIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMS8yXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTZcIj5cbiAgICAgICAgICB7Wy4uLkFycmF5KDQpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJjYXJkIHAtNiBhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzIgbWItNFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMy80IG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctZ3JheS0yMDAgcm91bmRlZCB3LTEvM1wiPjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgfT5cbiAgICAgIDxBbmFseXRpY3NQYWdlQ29udGVudCAvPlxuICAgIDwvU3VzcGVuc2U+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIlN1c3BlbnNlIiwiQ2hhcnRCYXJJY29uIiwiQ3VycmVuY3lEb2xsYXJJY29uIiwiQ2xvY2tJY29uIiwiQ3B1Q2hpcEljb24iLCJDaGVja0NpcmNsZUljb24iLCJDaGFydFBpZUljb24iLCJIYW5kVGh1bWJVcEljb24iLCJBcnJvd1BhdGhJY29uIiwiQW5hbHl0aWNzUGFnZUNvbnRlbnQiLCJhbmFseXRpY3NEYXRhIiwic2V0QW5hbHl0aWNzRGF0YSIsIm1vZGVsQW5hbHl0aWNzIiwic2V0TW9kZWxBbmFseXRpY3MiLCJjb25maWdBbmFseXRpY3MiLCJzZXRDb25maWdBbmFseXRpY3MiLCJ0aW1lU2VyaWVzRGF0YSIsInNldFRpbWVTZXJpZXNEYXRhIiwicHJldmlvdXNQZXJpb2REYXRhIiwic2V0UHJldmlvdXNQZXJpb2REYXRhIiwiY3VzdG9tQ29uZmlncyIsInNldEN1c3RvbUNvbmZpZ3MiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJ0aW1lUmFuZ2UiLCJzZXRUaW1lUmFuZ2UiLCJzZWxlY3RlZENvbmZpZyIsInNldFNlbGVjdGVkQ29uZmlnIiwic2hvd0ZpbHRlcnMiLCJzZXRTaG93RmlsdGVycyIsInN0YXJ0RGF0ZSIsInNldFN0YXJ0RGF0ZSIsImVuZERhdGUiLCJzZXRFbmREYXRlIiwibW9udGhseUJ1ZGdldCIsInNldE1vbnRobHlCdWRnZXQiLCJzaG93QnVkZ2V0U2V0dGluZ3MiLCJzZXRTaG93QnVkZ2V0U2V0dGluZ3MiLCJmZXRjaEN1c3RvbUNvbmZpZ3MiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJjb25maWdzIiwianNvbiIsImVyciIsImNvbnNvbGUiLCJmZXRjaEFuYWx5dGljc0RhdGEiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJjdXJyZW50U3RhcnREYXRlIiwiY3VycmVudEVuZERhdGUiLCJEYXRlIiwiYXBwZW5kIiwidG9JU09TdHJpbmciLCJzZXREYXRlIiwiZ2V0RGF0ZSIsInBhcnNlSW50IiwicHJldlBhcmFtcyIsInBlcmlvZExlbmd0aCIsImdldFRpbWUiLCJwcmV2U3RhcnREYXRlIiwicHJldkVuZERhdGUiLCJwcm92aWRlclJlc3BvbnNlIiwibW9kZWxSZXNwb25zZSIsInRpbWVTZXJpZXNSZXNwb25zZSIsInByZXZpb3VzUGVyaW9kUmVzcG9uc2UiLCJQcm9taXNlIiwiYWxsIiwidG9TdHJpbmciLCJFcnJvciIsInByb3ZpZGVyRGF0YSIsIm1vZGVsRGF0YSIsInRpbWVTZXJpZXNSZXN1bHQiLCJwcmV2aW91c1BlcmlvZFJlc3VsdCIsInByb2Nlc3NlZFRpbWVTZXJpZXMiLCJncm91cGVkX2RhdGEiLCJtYXAiLCJpdGVtIiwicGVyaW9kIiwiY29zdCIsInJlcXVlc3RzIiwidG9rZW5zIiwiaW5wdXRfdG9rZW5zIiwib3V0cHV0X3Rva2VucyIsIm1lc3NhZ2UiLCJmb3JtYXRDdXJyZW5jeSIsImFtb3VudCIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImN1cnJlbmN5IiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwiZm9ybWF0IiwiZm9ybWF0TnVtYmVyIiwibnVtIiwiY2FsY3VsYXRlVHJlbmQiLCJjdXJyZW50IiwicHJldmlvdXMiLCJwZXJjZW50YWdlIiwiaXNQb3NpdGl2ZSIsIk1hdGgiLCJhYnMiLCJnZW5lcmF0ZUNvc3RBbGVydHMiLCJhbGVydHMiLCJzdW1tYXJ5IiwicHJvamVjdGVkTW9udGhseUNvc3QiLCJ0b3RhbF9jb3N0IiwicHVzaCIsInR5cGUiLCJ0aXRsZSIsInZhbHVlIiwidG9GaXhlZCIsInN1Y2Nlc3NfcmF0ZSIsImF2ZXJhZ2VfY29zdF9wZXJfcmVxdWVzdCIsImdlbmVyYXRlUmVjb21tZW5kYXRpb25zIiwicmVjb21tZW5kYXRpb25zIiwicHJvdmlkZXJzIiwibW9kZWxzIiwibW9zdEV4cGVuc2l2ZU1vZGVsIiwic29ydCIsImEiLCJiIiwicG90ZW50aWFsU2F2aW5ncyIsImRlc2NyaXB0aW9uIiwibmFtZSIsInBvdGVudGlhbF9zYXZpbmdzIiwiaWNvbiIsImF2Z1Rva2Vuc1BlclJlcXVlc3QiLCJ0b3RhbF9pbnB1dF90b2tlbnMiLCJ0b3RhbF9vdXRwdXRfdG9rZW5zIiwidG90YWxfcmVxdWVzdHMiLCJyZXNldEZpbHRlcnMiLCJkaXYiLCJjbGFzc05hbWUiLCJBcnJheSIsIl8iLCJpIiwiaDEiLCJwIiwiYnV0dG9uIiwib25DbGljayIsImNvc3RFZmZpY2llbmN5IiwibWF4Iiwic3VjY2Vzc2Z1bF9yZXF1ZXN0cyIsImNvc3RBbGVydHMiLCJwcmV2aW91c1N1bW1hcnkiLCJjb3N0VHJlbmQiLCJyZXF1ZXN0VHJlbmQiLCJQcm9mZXNzaW9uYWxMaW5lQ2hhcnQiLCJkYXRhIiwibGVuZ3RoIiwibWF4Q29zdCIsImQiLCJtaW5Db3N0IiwibWluIiwibWF4UmVxdWVzdHMiLCJtaW5SZXF1ZXN0cyIsImNvc3RSYW5nZSIsInJlcXVlc3RSYW5nZSIsInN2ZyIsInZpZXdCb3giLCJkZWZzIiwibGluZWFyR3JhZGllbnQiLCJpZCIsIngxIiwieTEiLCJ4MiIsInkyIiwic3RvcCIsIm9mZnNldCIsInN0b3BDb2xvciIsInN0b3BPcGFjaXR5IiwicGF0dGVybiIsIndpZHRoIiwiaGVpZ2h0IiwicGF0dGVyblVuaXRzIiwicGF0aCIsImZpbGwiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInJlY3QiLCJwb2x5Z29uIiwicG9pbnRzIiwieCIsInkiLCJqb2luIiwicG9seWxpbmUiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJjb3N0WSIsInJlcXVlc3RZIiwiZyIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwiUHJvZmVzc2lvbmFsRG9udXRDaGFydCIsInRvdGFsIiwiY29sb3JzIiwicmFkaXVzIiwibm9ybWFsaXplZFJhZGl1cyIsImNpcmN1bWZlcmVuY2UiLCJQSSIsImN1bXVsYXRpdmVQZXJjZW50YWdlIiwiaW5kZXgiLCJzdHJva2VEYXNoYXJyYXkiLCJzdHJva2VEYXNob2Zmc2V0IiwiZmlsdGVyIiwic2xpY2UiLCJiYWNrZ3JvdW5kQ29sb3IiLCJzcGFuIiwiaW5wdXQiLCJwbGFjZWhvbGRlciIsInNlbGVjdCIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9wdGlvbiIsImgzIiwiYXZlcmFnZV9sYXRlbmN5IiwicmFuZG9tIiwiZmxvb3IiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwicHJvdmlkZXIiLCJ0ZCIsIm1vZGVsIiwiYXZnQ29zdCIsInN1YnN0cmluZyIsIkFuYWx5dGljc1BhZ2UiLCJmYWxsYmFjayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});