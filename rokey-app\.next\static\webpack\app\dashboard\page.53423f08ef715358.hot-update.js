"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,BeakerIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    var _user_user_metadata, _user_user_metadata_full_name, _user_user_metadata1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Start with false for progressive loading\n    const [initialLoad, setInitialLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            name: 'API Gateway',\n            status: 'operational'\n        },\n        {\n            name: 'Routing Engine',\n            status: 'operational'\n        },\n        {\n            name: 'Analytics',\n            status: 'degraded'\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // Progressive loading: render UI first, then load data\n            const loadData = {\n                \"DashboardPage.useEffect.loadData\": async ()=>{\n                    // Small delay to allow UI to render first\n                    await new Promise({\n                        \"DashboardPage.useEffect.loadData\": (resolve)=>setTimeout(resolve, 50)\n                    }[\"DashboardPage.useEffect.loadData\"]);\n                    // Load data in parallel for better performance\n                    const promises = [\n                        fetchAnalyticsData(),\n                        fetchRecentActivity(),\n                        checkSystemStatus()\n                    ];\n                    await Promise.allSettled(promises);\n                    setInitialLoad(false);\n                }\n            }[\"DashboardPage.useEffect.loadData\"];\n            loadData();\n            // Set up auto-refresh for activity feed every 30 seconds\n            const activityInterval = setInterval(fetchRecentActivity, 30000);\n            // Set up system status check every 60 seconds\n            const statusInterval = setInterval(checkSystemStatus, 60000);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    clearInterval(activityInterval);\n                    clearInterval(statusInterval);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                // Only show loading on initial load, not on refreshes\n                if (initialLoad) {\n                    setLoading(true);\n                }\n                // Get data for the last 30 days\n                const thirtyDaysAgo = new Date();\n                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n                const response = await fetch(\"/api/analytics/summary?startDate=\".concat(thirtyDaysAgo.toISOString(), \"&groupBy=day\"));\n                if (!response.ok) {\n                    throw new Error('Failed to fetch analytics data');\n                }\n                const data = await response.json();\n                setAnalyticsData(data);\n            } catch (err) {\n                setError(err.message);\n                console.error('Error fetching analytics:', err);\n            } finally{\n                if (initialLoad) {\n                    setLoading(false);\n                }\n            }\n        }\n    }[\"DashboardPage.useCallback[fetchAnalyticsData]\"], [\n        initialLoad\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    const fetchRecentActivity = async ()=>{\n        try {\n            // Fetch recent activity from the new activity API\n            const response = await fetch('/api/activity?limit=10');\n            if (!response.ok) {\n                throw new Error('Failed to fetch recent activity');\n            }\n            const data = await response.json();\n            const activities = data.activities.map((activity)=>({\n                    id: activity.id,\n                    action: activity.action,\n                    model: activity.model,\n                    time: activity.time,\n                    status: activity.status,\n                    details: activity.details\n                }));\n            setRecentActivity(activities);\n        } catch (err) {\n            console.error('Error fetching recent activity:', err);\n            // Set fallback activity data\n            setRecentActivity([\n                {\n                    id: '1',\n                    action: 'System initialized',\n                    model: 'RoKey',\n                    time: 'Just now',\n                    status: 'info'\n                }\n            ]);\n        }\n    };\n    const checkSystemStatus = async ()=>{\n        try {\n            const response = await fetch('/api/system-status');\n            if (!response.ok) {\n                throw new Error('Failed to fetch system status');\n            }\n            const data = await response.json();\n            const statusItems = data.checks.map((check)=>({\n                    name: check.name,\n                    status: check.status,\n                    lastChecked: new Date(check.lastChecked).toLocaleTimeString()\n                }));\n            setSystemStatus(statusItems);\n        } catch (err) {\n            console.error('Error checking system status:', err);\n            // Set fallback status\n            setSystemStatus([\n                {\n                    name: 'API Gateway',\n                    status: 'down',\n                    lastChecked: new Date().toLocaleTimeString()\n                },\n                {\n                    name: 'Routing Engine',\n                    status: 'down',\n                    lastChecked: new Date().toLocaleTimeString()\n                },\n                {\n                    name: 'Analytics',\n                    status: 'down',\n                    lastChecked: new Date().toLocaleTimeString()\n                }\n            ]);\n        }\n    };\n    const getTimeAgo = (date)=>{\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return 'Just now';\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \" minutes ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \" hours ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \" days ago\");\n    };\n    // Quick Actions handlers\n    const handleAddNewModel = ()=>{\n        router.push('/my-models');\n    };\n    const handleTestPlayground = ()=>{\n        router.push('/playground');\n    };\n    const handleViewLogs = ()=>{\n        router.push('/logs');\n    };\n    // Get user's first name for welcome message\n    const firstName = (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : (_user_user_metadata_full_name = _user_user_metadata1.full_name) === null || _user_user_metadata_full_name === void 0 ? void 0 : _user_user_metadata_full_name.split(' ')[0]) || 'there';\n    const stats = analyticsData ? [\n        {\n            name: 'Total Requests',\n            value: formatNumber(analyticsData.summary.total_requests),\n            change: 'Last 30 days',\n            changeType: 'neutral',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: 'Total Cost',\n            value: formatCurrency(analyticsData.summary.total_cost),\n            change: \"\".concat(formatCurrency(analyticsData.summary.average_cost_per_request), \" avg/request\"),\n            changeType: 'neutral',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: 'Success Rate',\n            value: \"\".concat(analyticsData.summary.success_rate.toFixed(1), \"%\"),\n            change: \"\".concat(formatNumber(analyticsData.summary.successful_requests), \" successful\"),\n            changeType: analyticsData.summary.success_rate >= 95 ? 'positive' : 'negative',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: 'Total Tokens',\n            value: formatNumber(analyticsData.summary.total_tokens),\n            change: \"\".concat(formatNumber(analyticsData.summary.total_input_tokens), \" in, \").concat(formatNumber(analyticsData.summary.total_output_tokens), \" out\"),\n            changeType: 'neutral',\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ] : [];\n    // Show loading only on initial load, not on subsequent visits\n    if (loading && initialLoad) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#1B1C1D] space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-slide-in\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold mb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\",\n                                children: [\n                                    \"Welcome \",\n                                    firstName,\n                                    \"! \\uD83D\\uDC4B\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-lg\",\n                            children: \"Here's what's happening with your LLM infrastructure today.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#2A2B2C] border border-white/10 rounded-xl p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-400 mb-4\",\n                            children: [\n                                \"Error loading analytics data: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"btn-primary\",\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 273,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#1B1C1D] space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\",\n                            children: [\n                                \"Welcome \",\n                                firstName,\n                                \"! \\uD83D\\uDC4B\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-lg\",\n                        children: \"Here's what's happening with your LLM infrastructure today.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#2A2B2C] border border-white/10 rounded-xl p-6 hover:border-white/20 hover:bg-[#2F3031] transition-all duration-200\",\n                        style: {\n                            animationDelay: \"\".concat(index * 100, \"ms\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-400\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-white mt-2\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-2 flex items-center \".concat(stat.changeType === 'positive' ? 'text-green-400' : stat.changeType === 'negative' ? 'text-red-400' : 'text-gray-500'),\n                                            children: [\n                                                stat.changeType !== 'neutral' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1 \".concat(stat.changeType === 'negative' ? 'rotate-180' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this),\n                                                stat.change\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg bg-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this)\n                    }, stat.name, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-[#2A2B2C] border border-white/10 rounded-xl p-6 animate-slide-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-4\",\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddNewModel,\n                                                className: \"btn-primary w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Add New Model\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTestPlayground,\n                                                className: \"btn-secondary w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Test in Playground\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleViewLogs,\n                                                className: \"btn-outline w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"View Logs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-[#2A2B2C] border border-white/10 rounded-xl p-6 animate-slide-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-4\",\n                                        children: \"System Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: systemStatus.map((system)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full mr-3 \".concat(system.status === 'operational' ? 'bg-green-500' : system.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300\",\n                                                                children: system.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium \".concat(system.status === 'operational' ? 'text-green-400' : system.status === 'degraded' ? 'text-yellow-400' : 'text-red-400'),\n                                                                children: system.status === 'operational' ? 'Operational' : system.status === 'degraded' ? 'Degraded' : 'Down'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            system.lastChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: system.lastChecked\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, system.name, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-xl p-6 animate-slide-in\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"Recent Activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: fetchRecentActivity,\n                                            className: \"text-orange-400 hover:text-orange-300 text-sm font-medium\",\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        recentActivity.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"No recent activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"Activity will appear here as you use the API\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this) : recentActivity.slice(-4).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 group overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mr-4 \".concat(activity.status === 'success' ? 'bg-green-500' : activity.status === 'warning' ? 'bg-yellow-500' : activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white font-medium break-words\",\n                                                                children: activity.action\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm break-words\",\n                                                                children: [\n                                                                    activity.model,\n                                                                    \" • \",\n                                                                    activity.time\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            activity.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed\",\n                                                                title: activity.details,\n                                                                children: activity.details\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400 group-hover:text-gray-300\",\n                                                        children: activity.status === 'error' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_BeakerIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this)),\n                                        recentActivity.length > 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // Navigate to logs page to see all activity\n                                                    window.location.href = '/logs';\n                                                },\n                                                className: \"text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors\",\n                                                children: [\n                                                    \"View All Activity (\",\n                                                    recentActivity.length,\n                                                    \")\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 ml-1\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 5l7 7-7 7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"W8IW2nRDujSuRPyyGICkyuxiVCU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});