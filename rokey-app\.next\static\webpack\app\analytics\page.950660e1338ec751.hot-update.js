"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,CurrencyDollarIcon,FunnelIcon,HandThumbUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HandThumbUpIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AnalyticsPageContent() {\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelAnalytics, setModelAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configAnalytics, setConfigAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Budget settings\n    const [monthlyBudget, setMonthlyBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Default $100/month\n    const [showBudgetSettings, setShowBudgetSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Build query parameters for current period\n                const params = new URLSearchParams();\n                let currentStartDate;\n                let currentEndDate = new Date();\n                if (startDate && endDate) {\n                    currentStartDate = new Date(startDate);\n                    currentEndDate = new Date(endDate);\n                    params.append('startDate', currentStartDate.toISOString());\n                    params.append('endDate', currentEndDate.toISOString());\n                } else if (timeRange) {\n                    currentStartDate = new Date();\n                    currentStartDate.setDate(currentStartDate.getDate() - parseInt(timeRange));\n                    params.append('startDate', currentStartDate.toISOString());\n                }\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                // Build query parameters for previous period (for comparison)\n                const prevParams = new URLSearchParams();\n                const periodLength = currentEndDate.getTime() - currentStartDate.getTime();\n                const prevStartDate = new Date(currentStartDate.getTime() - periodLength);\n                const prevEndDate = new Date(currentStartDate.getTime());\n                prevParams.append('startDate', prevStartDate.toISOString());\n                prevParams.append('endDate', prevEndDate.toISOString());\n                if (selectedConfig) {\n                    prevParams.append('customApiConfigId', selectedConfig);\n                }\n                // Fetch multiple analytics views\n                const [providerResponse, modelResponse, timeSeriesResponse, previousPeriodResponse] = await Promise.all([\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=model\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\")),\n                    fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=day\"))\n                ]);\n                if (!providerResponse.ok || !modelResponse.ok || !timeSeriesResponse.ok) {\n                    throw new Error('Failed to fetch analytics data');\n                }\n                const providerData = await providerResponse.json();\n                const modelData = await modelResponse.json();\n                const timeSeriesResult = await timeSeriesResponse.json();\n                const previousPeriodResult = previousPeriodResponse.ok ? await previousPeriodResponse.json() : null;\n                setAnalyticsData(providerData);\n                setModelAnalytics(modelData);\n                setPreviousPeriodData(previousPeriodResult);\n                // Process time series data\n                const processedTimeSeries = timeSeriesResult.grouped_data.map({\n                    \"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\": (item)=>({\n                            period: item.period,\n                            cost: item.cost,\n                            requests: item.requests,\n                            tokens: item.input_tokens + item.output_tokens\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\"]);\n                setTimeSeriesData(processedTimeSeries);\n            } catch (err) {\n                setError(err.message);\n                console.error('Error fetching analytics:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        fetchAnalyticsData,\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    const calculateTrend = (current, previous)=>{\n        if (previous === 0) return {\n            percentage: 0,\n            isPositive: true\n        };\n        const percentage = (current - previous) / previous * 100;\n        return {\n            percentage: Math.abs(percentage),\n            isPositive: percentage >= 0\n        };\n    };\n    const generateCostAlerts = ()=>{\n        const alerts = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        if (!summary) return alerts;\n        // Budget alert\n        const projectedMonthlyCost = summary.total_cost / parseInt(timeRange) * 30;\n        if (projectedMonthlyCost > monthlyBudget * 0.8) {\n            alerts.push({\n                type: projectedMonthlyCost > monthlyBudget ? 'danger' : 'warning',\n                title: projectedMonthlyCost > monthlyBudget ? 'Budget Exceeded' : 'Budget Warning',\n                message: \"Projected monthly cost: \".concat(formatCurrency(projectedMonthlyCost), \" (Budget: \").concat(formatCurrency(monthlyBudget), \")\"),\n                value: \"\".concat((projectedMonthlyCost / monthlyBudget * 100).toFixed(0), \"%\")\n            });\n        }\n        // Success rate alert\n        if (summary.success_rate < 95) {\n            alerts.push({\n                type: summary.success_rate < 90 ? 'danger' : 'warning',\n                title: 'Low Success Rate',\n                message: \"Current success rate is \".concat(summary.success_rate.toFixed(1), \"%. Consider reviewing failed requests.\"),\n                value: \"\".concat(summary.success_rate.toFixed(1), \"%\")\n            });\n        }\n        // High cost per request alert\n        if (summary.average_cost_per_request > 0.01) {\n            alerts.push({\n                type: 'warning',\n                title: 'High Cost Per Request',\n                message: \"Average cost per request is \".concat(formatCurrency(summary.average_cost_per_request), \". Consider optimizing model usage.\"),\n                value: formatCurrency(summary.average_cost_per_request)\n            });\n        }\n        return alerts;\n    };\n    const generateRecommendations = ()=>{\n        const recommendations = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        const providers = (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data) || [];\n        const models = (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data) || [];\n        if (!summary) return recommendations;\n        // Cost optimization recommendations\n        const mostExpensiveModel = models.sort((a, b)=>b.cost - a.cost)[0];\n        if (mostExpensiveModel && mostExpensiveModel.cost > summary.total_cost * 0.3) {\n            const potentialSavings = mostExpensiveModel.cost * 0.2; // Assume 20% savings possible\n            recommendations.push({\n                type: 'cost_optimization',\n                title: 'Optimize Expensive Model Usage',\n                description: \"\".concat(mostExpensiveModel.name, \" accounts for \").concat((mostExpensiveModel.cost / summary.total_cost * 100).toFixed(1), \"% of your costs. Consider using a more cost-effective alternative for simpler tasks.\"),\n                potential_savings: potentialSavings,\n                icon: _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n            });\n        }\n        // Performance recommendations\n        if (summary.success_rate < 98) {\n            recommendations.push({\n                type: 'performance',\n                title: 'Improve Request Reliability',\n                description: \"Your success rate is \".concat(summary.success_rate.toFixed(1), \"%. Implement retry logic and error handling to improve reliability.\"),\n                icon: _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            });\n        }\n        // Efficiency recommendations\n        const avgTokensPerRequest = (summary.total_input_tokens + summary.total_output_tokens) / summary.total_requests;\n        if (avgTokensPerRequest > 1000) {\n            recommendations.push({\n                type: 'efficiency',\n                title: 'Optimize Token Usage',\n                description: \"Average \".concat(formatNumber(avgTokensPerRequest), \" tokens per request. Consider breaking down large prompts or using more efficient models.\"),\n                icon: _barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n            });\n        }\n        return recommendations;\n    };\n    const resetFilters = ()=>{\n        setTimeRange('30');\n        setSelectedConfig('');\n        setStartDate('');\n        setEndDate('');\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 313,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-6\",\n                    children: \"\\uD83D\\uDCCA Advanced Analytics\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: [\n                                \"Error loading analytics: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"btn-primary\",\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 333,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const costEfficiency = summary ? summary.total_cost / Math.max(summary.successful_requests, 1) : 0;\n    const projectedMonthlyCost = summary ? summary.total_cost / parseInt(timeRange) * 30 : 0;\n    // Generate insights\n    const costAlerts = generateCostAlerts();\n    const recommendations = generateRecommendations();\n    // Calculate trends vs previous period\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : null;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : null;\n    // Professional Line Chart Component (like reference)\n    const ProfessionalLineChart = (param)=>{\n        let { data } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, this);\n        }\n        const maxCost = Math.max(...data.map((d)=>d.cost));\n        const minCost = Math.min(...data.map((d)=>d.cost));\n        const maxRequests = Math.max(...data.map((d)=>d.requests));\n        const minRequests = Math.min(...data.map((d)=>d.requests));\n        const costRange = maxCost - minCost || 1;\n        const requestRange = maxRequests - minRequests || 1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-64 w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-full h-full\",\n                viewBox: \"0 0 600 200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"costGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"requestGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                                id: \"grid\",\n                                width: \"40\",\n                                height: \"40\",\n                                patternUnits: \"userSpaceOnUse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 40 0 L 0 0 0 40\",\n                                    fill: \"none\",\n                                    stroke: \"#f1f5f9\",\n                                    strokeWidth: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"600\",\n                        height: \"200\",\n                        fill: \"url(#grid)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#costGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#requestGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#8b5cf6\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#06b6d4\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 11\n                    }, this),\n                    data.map((d, i)=>{\n                        const x = i / Math.max(data.length - 1, 1) * 600;\n                        const costY = 200 - (d.cost - minCost) / costRange * 160;\n                        const requestY = 200 - (d.requests - minRequests) / requestRange * 160;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: costY,\n                                    r: \"3\",\n                                    fill: \"#8b5cf6\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatCurrency(d.cost))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: requestY,\n                                    r: \"3\",\n                                    fill: \"#06b6d4\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatNumber(d.requests), \" requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 15\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 380,\n            columnNumber: 7\n        }, this);\n    };\n    // Professional Donut Chart Component (like reference)\n    const ProfessionalDonutChart = (param)=>{\n        let { data, total } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-48 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 491,\n                columnNumber: 9\n            }, this);\n        }\n        // Professional color palette like the reference\n        const colors = [\n            '#f43f5e',\n            '#8b5cf6',\n            '#06b6d4',\n            '#10b981',\n            '#f59e0b',\n            '#ef4444'\n        ];\n        const radius = 70;\n        const strokeWidth = 16;\n        const normalizedRadius = radius - strokeWidth * 0.5;\n        const circumference = normalizedRadius * 2 * Math.PI;\n        let cumulativePercentage = 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"160\",\n                            height: \"160\",\n                            className: \"transform -rotate-90\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"80\",\n                                    cy: \"80\",\n                                    r: normalizedRadius,\n                                    stroke: \"#f8fafc\",\n                                    strokeWidth: strokeWidth,\n                                    fill: \"transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                data.map((item, index)=>{\n                                    const percentage = item.cost / total * 100;\n                                    const strokeDasharray = \"\".concat(percentage / 100 * circumference, \" \").concat(circumference);\n                                    const strokeDashoffset = -(cumulativePercentage / 100 * circumference);\n                                    cumulativePercentage += percentage;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"80\",\n                                        cy: \"80\",\n                                        r: normalizedRadius,\n                                        stroke: colors[index % colors.length],\n                                        strokeWidth: strokeWidth,\n                                        strokeDasharray: strokeDasharray,\n                                        strokeDashoffset: strokeDashoffset,\n                                        fill: \"transparent\",\n                                        strokeLinecap: \"round\",\n                                        className: \"transition-all duration-300 hover:opacity-80 cursor-pointer\",\n                                        style: {\n                                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                        }\n                                    }, index, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: data.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Providers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 w-full\",\n                    children: data.slice(0, 4).map((item, index)=>{\n                        const percentage = (item.cost / total * 100).toFixed(1);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between py-2 px-3 rounded-lg hover:bg-white/5 transition-colors duration-150\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mr-3 shadow-sm\",\n                                            style: {\n                                                backgroundColor: colors[index]\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-white capitalize\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-semibold text-white\",\n                                            children: [\n                                                percentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: formatCurrency(item.cost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#1B1C1D] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\",\n                                        children: \"Analytics Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Track your LLM usage, costs, and performance across all providers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: timeRange,\n                                    onChange: (e)=>setTimeRange(e.target.value),\n                                    className: \"px-4 py-2 bg-[#2A2B2C] border border-white/20 rounded-lg text-sm text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"7\",\n                                            children: \"Last 7 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"30\",\n                                            children: \"Last 30 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"90\",\n                                            children: \"Last 90 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 inline\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, this),\n                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Advanced Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Time Range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: timeRange,\n                                            onChange: (e)=>setTimeRange(e.target.value),\n                                            className: \"input-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"7\",\n                                                    children: \"Last 7 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"30\",\n                                                    children: \"Last 30 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"90\",\n                                                    children: \"Last 90 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"365\",\n                                                    children: \"Last year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"API Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedConfig,\n                                            onChange: (e)=>setSelectedConfig(e.target.value),\n                                            className: \"input-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"All Configurations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: config.id,\n                                                        children: config.name\n                                                    }, config.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: startDate,\n                                            onChange: (e)=>setStartDate(e.target.value),\n                                            className: \"input-field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: endDate,\n                                            onChange: (e)=>setEndDate(e.target.value),\n                                            className: \"input-field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"btn-primary\",\n                                    children: \"Apply Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetFilters,\n                                    className: \"btn-secondary\",\n                                    children: \"Reset Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 624,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Usage Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Cost trends over time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-purple-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-cyan-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfessionalLineChart, {\n                                    data: timeSeriesData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 pt-6 border-t border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                                                children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total spend this period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-1\",\n                                            children: \"Cost Distribution\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"By provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 13\n                                }, this),\n                                (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.length) && summary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfessionalDonutChart, {\n                                    data: analyticsData.grouped_data,\n                                    total: summary.total_cost\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"No provider data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 9\n                }, this),\n                summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Total Request Made\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: formatNumber(summary.total_requests)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    requestTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(requestTrend.isPositive ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'),\n                                                        children: [\n                                                            requestTrend.isPositive ? '+' : '',\n                                                            requestTrend.percentage.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 750,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 749,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Average Latency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: summary.average_latency ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '0ms'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400\",\n                                                        children: \"+20.34%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"User Feedback\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: [\n                                                            summary.success_rate.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(summary.success_rate >= 95 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'),\n                                                        children: summary.success_rate >= 95 ? '+1.34%' : '-1.34%'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 793,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-400 mb-1\",\n                                                children: \"Total Cost\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-3\",\n                                                        children: formatCurrency(summary.total_cost)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center px-2 py-1 rounded text-xs font-medium \".concat(costTrend.isPositive ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'),\n                                                        children: [\n                                                            costTrend.isPositive ? '+' : '',\n                                                            costTrend.percentage.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-500/20 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 747,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Cost\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-white mr-3\",\n                                                    children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-red-500/20 text-red-400\",\n                                                    children: \"10.46%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"This period\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500 h-2 rounded-full\",\n                                                style: {\n                                                    width: '75%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Latency\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-white mr-3\",\n                                                    children: (summary === null || summary === void 0 ? void 0 : summary.average_latency) ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '0ms'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400\",\n                                                    children: \"112%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 869,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Average response\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: (summary === null || summary === void 0 ? void 0 : summary.average_latency) ? \"\".concat(summary.average_latency.toFixed(0), \"ms\") : '0ms'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-500 h-2 rounded-full\",\n                                                style: {\n                                                    width: '60%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-gray-400 mb-1\",\n                                            children: \"Tokens Used\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-white mr-3\",\n                                                    children: summary ? \"\".concat(((summary.total_input_tokens + summary.total_output_tokens) / 1000000).toFixed(1), \"M\") : '0M'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400\",\n                                                    children: \"+9.39%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Total processed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: summary ? formatNumber(summary.total_input_tokens + summary.total_output_tokens) : '0'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 907,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-500 h-2 rounded-full\",\n                                                style: {\n                                                    width: '85%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 841,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-1\",\n                                            children: \"Provider Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Detailed breakdown by provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 13\n                                }, this),\n                                (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-white/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Provider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 932,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Share\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-white/5\",\n                                                children: analyticsData.grouped_data.sort((a, b)=>b.cost - a.cost).map((provider, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444',\n                                                        '#8b5cf6'\n                                                    ];\n                                                    const percentage = (provider.cost / ((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 1) * 100).toFixed(1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-white/5 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 947,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-white capitalize\",\n                                                                            children: provider.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 951,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-white\",\n                                                                children: formatCurrency(provider.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-400\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/10 text-gray-300\",\n                                                                    children: [\n                                                                        percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 961,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 960,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 944,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No provider data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#2A2B2C] border border-white/10 rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-1\",\n                                            children: \"Top Models\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Most expensive models by cost\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 13\n                                }, this),\n                                (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-white/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Model\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 992,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-400\",\n                                                            children: \"Avg/Request\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 994,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 989,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-white/5\",\n                                                children: modelAnalytics.grouped_data.sort((a, b)=>b.cost - a.cost).slice(0, 5).map((model, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444'\n                                                    ];\n                                                    const avgCost = model.cost / Math.max(model.requests, 1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-white/5 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1008,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-white\",\n                                                                                    children: model.name.length > 20 ? model.name.substring(0, 20) + '...' : model.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 1013,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                                                    children: [\n                                                                                        formatNumber(model.input_tokens + model.output_tokens),\n                                                                                        \" tokens\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 1014,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1012,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1007,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1006,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-white\",\n                                                                children: formatCurrency(model.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1020,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-300\",\n                                                                children: formatNumber(model.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1023,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-300\",\n                                                                children: formatCurrency(avgCost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1026,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, model.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 997,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_CurrencyDollarIcon_FunnelIcon_HandThumbUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No model data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1038,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 919,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 588,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 587,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"qgClNvt3fYHqAHTRr8MfhVCyyr4=\");\n_c = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 11\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1062,\n                            columnNumber: 11\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1060,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1069,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 13\n                        }, void 0))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1064,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1059,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1075,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 1058,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AnalyticsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AnalyticsPageContent\");\n$RefreshReg$(_c1, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});